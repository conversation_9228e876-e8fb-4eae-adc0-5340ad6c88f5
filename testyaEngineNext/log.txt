len = 127
  [PER got  1<=1016 bits => span 1 +10[1..1016]:26 (1015) => 0x0] (./asn_bit_data.c:132)
  [PER got  5<=1015 bits => span 6 +10[6..1016]:26 (1010) => 0x9] (./asn_bit_data.c:132)
CHOICE RasMessage got index 9 in range 5 (./constr_CHOICE.c:871)
Discovered CHOICE RasMessage encodes admissionRequest (./constr_CHOICE.c:903)
Decoding AdmissionRequest as SEQUENCE (UPER) (./constr_SEQUENCE.c:1098)
  [PER got  1<=1010 bits => span 7 +10[7..1016]:26 (1009) => 0x1] (./asn_bit_data.c:132)
  [PER got  7<=1009 bits => span 14 +10[14..1016]:26 (1002) => 0x24] (./asn_bit_data.c:132)
Read in presence bitmap for AdmissionRequest of 7 bits (48..) (./constr_SEQUENCE.c:1120)
Decoding member "requestSeqNum" in AdmissionRequest (./constr_SEQUENCE.c:1170)
Decoding NativeInteger RequestSeqNum (UPER) (./NativeInteger.c:268)
Integer with range 16 bits (./INTEGER.c:629)
  [PER got 16<=1002 bits => span 30 +11[22..1008]:90 (986) => 0x17d6] (./asn_bit_data.c:132)
Got value 6102 + low 1 (./INTEGER.c:650)
NativeInteger RequestSeqNum got value 6103 (./NativeInteger.c:284)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:125)
Decoding member "callType" in AdmissionRequest (./constr_SEQUENCE.c:1170)
  [PER got  1<=986 bits => span 31 +13[7..992]:59 (985) => 0x0] (./asn_bit_data.c:132)
  [PER got  2<=985 bits => span 33 +13[9..992]:59 (983) => 0x2] (./asn_bit_data.c:132)
CHOICE CallType got index 2 in range 2 (./constr_CHOICE.c:871)
Discovered CHOICE CallType encodes nToOne (./constr_CHOICE.c:903)
  [PER got  1<= 7 bits => span 1 +0[1..7]:48 (6) => 0x0] (./asn_bit_data.c:132)
Member AdmissionRequest->callModel is optional, p=0 (1->7) (./constr_SEQUENCE.c:1150)
Decoding member "endpointIdentifier" in AdmissionRequest (./constr_SEQUENCE.c:1170)
PER Decoding non-extensible size 1 .. 128 bits 7 (./OCTET_STRING.c:1422)
  [PER got  7<=983 bits => span 40 +14[8..984]:03 (976) => 0x3] (./asn_bit_data.c:132)
Got PER length eb 7, len 4, once (EndpointIdentifier) (./OCTET_STRING.c:1483)
Expanding 4 characters into (0..65533):16 (./OCTET_STRING.c:1220)
  [PER got 24<=976 bits => span 64 +15[24..976]:c0 (952) => 0xc00035] (./asn_bit_data.c:132)
  [PER got 24<=952 bits => span 88 +2[24..952]:00 (928) => 0x3200] (./asn_bit_data.c:132)
  [PER got 16<=928 bits => span 104 +5[16..928]:42 (912) => 0x4200] (./asn_bit_data.c:132)
  [PER got  1<= 6 bits => span 2 +0[2..7]:48 (5) => 0x1] (./asn_bit_data.c:132)
Member AdmissionRequest->destinationInfo is optional, p=1 (2->7) (./constr_SEQUENCE.c:1150)
Decoding member "destinationInfo" in AdmissionRequest (./constr_SEQUENCE.c:1170)
  [PER got  8<=912 bits => span 112 +7[8..912]:38 (904) => 0x38] (./asn_bit_data.c:132)
Got to decode 56 elements (eff -1) (./constr_SET_OF.c:960)
SET OF AliasAddress decoding (./constr_SET_OF.c:967)
  [PER got  1<=904 bits => span 113 +8[1..904]:00 (903) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<=903 bits => span 114 +8[2..904]:00 (902) => 0x0] (./asn_bit_data.c:132)
CHOICE AliasAddress got index 0 in range 1 (./constr_CHOICE.c:871)
Discovered CHOICE AliasAddress encodes dialledDigits (./constr_CHOICE.c:903)
PER Decoding non-extensible size 1 .. 128 bits 7 (./OCTET_STRING.c:1422)
  [PER got  7<=902 bits => span 121 +8[9..904]:00 (895) => 0x0] (./asn_bit_data.c:132)
Got PER length eb 7, len 1, once (IA5String) (./OCTET_STRING.c:1483)
Expanding 1 characters into (35..57):4 (./OCTET_STRING.c:1220)
  [PER got  4<=895 bits => span 125 +9[5..896]:38 (891) => 0x7] (./asn_bit_data.c:132)
destinationInfo SET OF AliasAddress decoded 0, 0x1481a520 (./constr_SET_OF.c:970)
SET OF AliasAddress decoding (./constr_SET_OF.c:967)
  [PER got  1<=891 bits => span 126 +9[6..896]:38 (890) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<=890 bits => span 127 +9[7..896]:38 (889) => 0x0] (./asn_bit_data.c:132)
CHOICE AliasAddress got index 0 in range 1 (./constr_CHOICE.c:871)
Discovered CHOICE AliasAddress encodes dialledDigits (./constr_CHOICE.c:903)
PER Decoding non-extensible size 1 .. 128 bits 7 (./OCTET_STRING.c:1422)
  [PER got  7<=889 bits => span 134 +9[14..896]:38 (882) => 0x0] (./asn_bit_data.c:132)
Got PER length eb 7, len 1, once (IA5String) (./OCTET_STRING.c:1483)
Expanding 1 characters into (35..57):4 (./OCTET_STRING.c:1220)
  [PER got  4<=882 bits => span 138 +10[10..888]:00 (878) => 0x0] (./asn_bit_data.c:132)
destinationInfo SET OF AliasAddress decoded 0, 0x1481a6b0 (./constr_SET_OF.c:970)
SET OF AliasAddress decoding (./constr_SET_OF.c:967)
  [PER got  1<=878 bits => span 139 +11[3..880]:30 (877) => 0x1] (./asn_bit_data.c:132)
  [PER got  7<=877 bits => span 146 +11[10..880]:30 (870) => 0x40] (./asn_bit_data.c:132)
  [PER got  2<=870 bits => span 148 +12[4..872]:00 (868) => 0x0] (./asn_bit_data.c:132)
Discovered CHOICE AliasAddress encodes url-ID (./constr_CHOICE.c:903)
Getting open type IA5String... (./per_opentype.c:83)
  [PER got  8<=868 bits => span 156 +12[12..872]:00 (860) => 0x4] (./asn_bit_data.c:132)
  [PER got 24<=860 bits => span 180 +13[28..864]:46 (836) => 0x600430] (./asn_bit_data.c:132)
  [PER got  8<=836 bits => span 188 +0[12..840]:00 (828) => 0x3] (./asn_bit_data.c:132)
Getting open type IA5String encoded in 4 bytes (./per_opentype.c:108)
PER Decoding non-extensible size 1 .. 512 bits 9 (./OCTET_STRING.c:1422)
  [PER got  9<=32 bits => span 9 +0[9..32]:60 (23) => 0xc0] (./asn_bit_data.c:132)
Got PER length eb 9, len 193, once (IA5String) (./OCTET_STRING.c:1483)
Expanding 193 characters into (0..127):7 (./OCTET_STRING.c:1220)
  [PER got  7<=23 bits => span 16 +1[8..24]:04 (16) => 0x4] (./asn_bit_data.c:132)
  [PER got  7<=16 bits => span 23 +2[7..16]:30 (9) => 0x18] (./asn_bit_data.c:132)
  [PER got  7<= 9 bits => span 30 +2[14..16]:30 (2) => 0x0] (./asn_bit_data.c:132)
Failed to decode url-ID in AliasAddress (CHOICE) 2 (./constr_CHOICE.c:914)
destinationInfo SET OF AliasAddress decoded 2, 0x1481a840 (./constr_SET_OF.c:970)
Failed decoding AliasAddress of destinationInfo (SET OF) (./constr_SET_OF.c:985)
Freeing AliasAddress as CHOICE (./constr_CHOICE.c:1065)
Freeing IA5String as OCTET STRING (./OCTET_STRING.c:1722)
Failed decode destinationInfo in AdmissionRequest (./constr_SEQUENCE.c:1179)
Failed to decode admissionRequest in RasMessage (CHOICE) 2 (./constr_CHOICE.c:914)
H225RAS: UPER decode complete failed (consumed: 0 bytes)
Freeing RasMessage as CHOICE (./constr_CHOICE.c:1065)
Freeing AdmissionRequest as SEQUENCE (./constr_SEQUENCE.c:994)
Freeing RequestSeqNum as INTEGER (1, 0x1481a1f8, Native) (./NativeInteger.c:362)
Freeing CallType as CHOICE (./constr_CHOICE.c:1065)
Freeing EndpointIdentifier as OCTET STRING (./OCTET_STRING.c:1722)
Freeing AliasAddress as CHOICE (./constr_CHOICE.c:1065)
Freeing IA5String as OCTET STRING (./OCTET_STRING.c:1722)
Freeing AliasAddress as CHOICE (./constr_CHOICE.c:1065)
Freeing IA5String as OCTET STRING (./OCTET_STRING.c:1722)
Freeing BandWidth as INTEGER (1, 0x1481a2a0, Native) (./NativeInteger.c:362)
Freeing CallReferenceValue as INTEGER (1, 0x1481a2a8, Native) (./NativeInteger.c:362)
Freeing ConferenceIdentifier as OCTET STRING (./OCTET_STRING.c:1722)
  [PER got  1<=1016 bits => span 1 +10[1..1016]:26 (1015) => 0x0] (./asn_bit_data.c:132)
  [PER got  5<=1015 bits => span 6 +10[6..1016]:26 (1010) => 0x9] (./asn_bit_data.c:132)
CHOICE RasMessage got index 9 in range 5 (./constr_CHOICE.c:871)
Discovered CHOICE RasMessage encodes admissionRequest (./constr_CHOICE.c:903)
Decoding AdmissionRequest as SEQUENCE (UPER) (./constr_SEQUENCE.c:1098)
  [PER got  1<=1010 bits => span 7 +10[7..1016]:26 (1009) => 0x1] (./asn_bit_data.c:132)
  [PER got  7<=1009 bits => span 14 +10[14..1016]:26 (1002) => 0x24] (./asn_bit_data.c:132)
Read in presence bitmap for AdmissionRequest of 7 bits (48..) (./constr_SEQUENCE.c:1120)
Decoding member "requestSeqNum" in AdmissionRequest (./constr_SEQUENCE.c:1170)
Decoding NativeInteger RequestSeqNum (UPER) (./NativeInteger.c:268)
Integer with range 16 bits (./INTEGER.c:629)
  [PER got 16<=1002 bits => span 30 +11[22..1008]:90 (986) => 0x17d6] (./asn_bit_data.c:132)
Got value 6102 + low 1 (./INTEGER.c:650)
NativeInteger RequestSeqNum got value 6103 (./NativeInteger.c:284)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:125)
Decoding member "callType" in AdmissionRequest (./constr_SEQUENCE.c:1170)
  [PER got  1<=986 bits => span 31 +13[7..992]:59 (985) => 0x0] (./asn_bit_data.c:132)
  [PER got  2<=985 bits => span 33 +13[9..992]:59 (983) => 0x2] (./asn_bit_data.c:132)
CHOICE CallType got index 2 in range 2 (./constr_CHOICE.c:871)
Discovered CHOICE CallType encodes nToOne (./constr_CHOICE.c:903)
  [PER got  1<= 7 bits => span 1 +0[1..7]:48 (6) => 0x0] (./asn_bit_data.c:132)
Member AdmissionRequest->callModel is optional, p=0 (1->7) (./constr_SEQUENCE.c:1150)
Decoding member "endpointIdentifier" in AdmissionRequest (./constr_SEQUENCE.c:1170)
PER Decoding non-extensible size 1 .. 128 bits 7 (./OCTET_STRING.c:1422)
  [PER got  7<=983 bits => span 40 +14[8..984]:03 (976) => 0x3] (./asn_bit_data.c:132)
Got PER length eb 7, len 4, once (EndpointIdentifier) (./OCTET_STRING.c:1483)
Expanding 4 characters into (0..65533):16 (./OCTET_STRING.c:1220)
  [PER got 24<=976 bits => span 64 +15[24..976]:c0 (952) => 0xc00035] (./asn_bit_data.c:132)
  [PER got 24<=952 bits => span 88 +2[24..952]:00 (928) => 0x3200] (./asn_bit_data.c:132)
  [PER got 16<=928 bits => span 104 +5[16..928]:42 (912) => 0x4200] (./asn_bit_data.c:132)
  [PER got  1<= 6 bits => span 2 +0[2..7]:48 (5) => 0x1] (./asn_bit_data.c:132)
Member AdmissionRequest->destinationInfo is optional, p=1 (2->7) (./constr_SEQUENCE.c:1150)
Decoding member "destinationInfo" in AdmissionRequest (./constr_SEQUENCE.c:1170)
  [PER got  8<=912 bits => span 112 +7[8..912]:38 (904) => 0x38] (./asn_bit_data.c:132)
Got to decode 56 elements (eff -1) (./constr_SET_OF.c:960)
SET OF AliasAddress decoding (./constr_SET_OF.c:967)
  [PER got  1<=904 bits => span 113 +8[1..904]:00 (903) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<=903 bits => span 114 +8[2..904]:00 (902) => 0x0] (./asn_bit_data.c:132)
CHOICE AliasAddress got index 0 in range 1 (./constr_CHOICE.c:871)
Discovered CHOICE AliasAddress encodes dialledDigits (./constr_CHOICE.c:903)
PER Decoding non-extensible size 1 .. 128 bits 7 (./OCTET_STRING.c:1422)
  [PER got  7<=902 bits => span 121 +8[9..904]:00 (895) => 0x0] (./asn_bit_data.c:132)
Got PER length eb 7, len 1, once (IA5String) (./OCTET_STRING.c:1483)
Expanding 1 characters into (35..57):4 (./OCTET_STRING.c:1220)
  [PER got  4<=895 bits => span 125 +9[5..896]:38 (891) => 0x7] (./asn_bit_data.c:132)
destinationInfo SET OF AliasAddress decoded 0, 0x1481ac10 (./constr_SET_OF.c:970)
SET OF AliasAddress decoding (./constr_SET_OF.c:967)
  [PER got  1<=891 bits => span 126 +9[6..896]:38 (890) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<=890 bits => span 127 +9[7..896]:38 (889) => 0x0] (./asn_bit_data.c:132)
CHOICE AliasAddress got index 0 in range 1 (./constr_CHOICE.c:871)
Discovered CHOICE AliasAddress encodes dialledDigits (./constr_CHOICE.c:903)
PER Decoding non-extensible size 1 .. 128 bits 7 (./OCTET_STRING.c:1422)
  [PER got  7<=889 bits => span 134 +9[14..896]:38 (882) => 0x0] (./asn_bit_data.c:132)
Got PER length eb 7, len 1, once (IA5String) (./OCTET_STRING.c:1483)
Expanding 1 characters into (35..57):4 (./OCTET_STRING.c:1220)
  [PER got  4<=882 bits => span 138 +10[10..888]:00 (878) => 0x0] (./asn_bit_data.c:132)
destinationInfo SET OF AliasAddress decoded 0, 0x1481ada0 (./constr_SET_OF.c:970)
SET OF AliasAddress decoding (./constr_SET_OF.c:967)
  [PER got  1<=878 bits => span 139 +11[3..880]:30 (877) => 0x1] (./asn_bit_data.c:132)
  [PER got  7<=877 bits => span 146 +11[10..880]:30 (870) => 0x40] (./asn_bit_data.c:132)
  [PER got  2<=870 bits => span 148 +12[4..872]:00 (868) => 0x0] (./asn_bit_data.c:132)
Discovered CHOICE AliasAddress encodes url-ID (./constr_CHOICE.c:903)
Getting open type IA5String... (./per_opentype.c:83)
  [PER got  8<=868 bits => span 156 +12[12..872]:00 (860) => 0x4] (./asn_bit_data.c:132)
  [PER got 24<=860 bits => span 180 +13[28..864]:46 (836) => 0x600430] (./asn_bit_data.c:132)
  [PER got  8<=836 bits => span 188 +0[12..840]:00 (828) => 0x3] (./asn_bit_data.c:132)
Getting open type IA5String encoded in 4 bytes (./per_opentype.c:108)
PER Decoding non-extensible size 1 .. 512 bits 9 (./OCTET_STRING.c:1422)
  [PER got  9<=32 bits => span 9 +0[9..32]:60 (23) => 0xc0] (./asn_bit_data.c:132)
Got PER length eb 9, len 193, once (IA5String) (./OCTET_STRING.c:1483)
Expanding 193 characters into (0..127):7 (./OCTET_STRING.c:1220)
  [PER got  7<=23 bits => span 16 +1[8..24]:04 (16) => 0x4] (./asn_bit_data.c:132)
  [PER got  7<=16 bits => span 23 +2[7..16]:30 (9) => 0x18] (./asn_bit_data.c:132)
  [PER got  7<= 9 bits => span 30 +2[14..16]:30 (2) => 0x0] (./asn_bit_data.c:132)
Failed to decode url-ID in AliasAddress (CHOICE) 2 (./constr_CHOICE.c:914)
destinationInfo SET OF AliasAddress decoded 2, 0x1481af30 (./constr_SET_OF.c:970)
Failed decoding AliasAddress of destinationInfo (SET OF) (./constr_SET_OF.c:985)
Freeing AliasAddress as CHOICE (./constr_CHOICE.c:1065)
Freeing IA5String as OCTET STRING (./OCTET_STRING.c:1722)
Failed decode destinationInfo in AdmissionRequest (./constr_SEQUENCE.c:1179)
Failed to decode admissionRequest in RasMessage (CHOICE) 2 (./constr_CHOICE.c:914)
Freeing RasMessage as CHOICE (./constr_CHOICE.c:1065)
Freeing AdmissionRequest as SEQUENCE (./constr_SEQUENCE.c:994)
Freeing RequestSeqNum as INTEGER (1, 0x1481a948, Native) (./NativeInteger.c:362)
Freeing CallType as CHOICE (./constr_CHOICE.c:1065)
Freeing EndpointIdentifier as OCTET STRING (./OCTET_STRING.c:1722)
Freeing AliasAddress as CHOICE (./constr_CHOICE.c:1065)
Freeing IA5String as OCTET STRING (./OCTET_STRING.c:1722)
Freeing AliasAddress as CHOICE (./constr_CHOICE.c:1065)
Freeing IA5String as OCTET STRING (./OCTET_STRING.c:1722)
Freeing BandWidth as INTEGER (1, 0x1481a9f0, Native) (./NativeInteger.c:362)
Freeing CallReferenceValue as INTEGER (1, 0x1481a9f8, Native) (./NativeInteger.c:362)
Freeing ConferenceIdentifier as OCTET STRING (./OCTET_STRING.c:1722)
  [PER got  1<=1015 bits => span 1 +10[2..1016]:26 (1014) => 0x0] (./asn_bit_data.c:132)
  [PER got  5<=1014 bits => span 6 +10[7..1016]:26 (1009) => 0x13] (./asn_bit_data.c:132)
CHOICE RasMessage got index 19 in range 5 (./constr_CHOICE.c:871)
Discovered CHOICE RasMessage encodes locationConfirm (./constr_CHOICE.c:903)
Decoding LocationConfirm as SEQUENCE (UPER) (./constr_SEQUENCE.c:1098)
  [PER got  1<=1009 bits => span 7 +10[8..1016]:26 (1008) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<=1008 bits => span 8 +11[1..1008]:90 (1007) => 0x1] (./asn_bit_data.c:132)
Read in presence bitmap for LocationConfirm of 1 bits (80..) (./constr_SEQUENCE.c:1120)
Decoding member "requestSeqNum" in LocationConfirm (./constr_SEQUENCE.c:1170)
Decoding NativeInteger RequestSeqNum (UPER) (./NativeInteger.c:268)
Integer with range 16 bits (./INTEGER.c:629)
  [PER got 16<=1007 bits => span 24 +11[17..1008]:90 (991) => 0x20be] (./asn_bit_data.c:132)
Got value 8382 + low 1 (./INTEGER.c:650)
NativeInteger RequestSeqNum got value 8383 (./NativeInteger.c:284)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:125)
Decoding member "callSignalAddress" in LocationConfirm (./constr_SEQUENCE.c:1170)
  [PER got  1<=991 bits => span 25 +13[2..992]:59 (990) => 0x1] (./asn_bit_data.c:132)
  [PER got  7<=990 bits => span 32 +13[9..992]:59 (983) => 0x32] (./asn_bit_data.c:132)
Failed to decode element TransportAddress (./constr_CHOICE.c:882)
Failed decode callSignalAddress in LocationConfirm (./constr_SEQUENCE.c:1179)
Failed to decode locationConfirm in RasMessage (CHOICE) 2 (./constr_CHOICE.c:914)
Freeing RasMessage as CHOICE (./constr_CHOICE.c:1065)
Freeing LocationConfirm as SEQUENCE (./constr_SEQUENCE.c:994)
Freeing RequestSeqNum as INTEGER (1, 0x1481b038, Native) (./NativeInteger.c:362)
Freeing TransportAddress as CHOICE (./constr_CHOICE.c:1065)
Freeing TransportAddress as CHOICE (./constr_CHOICE.c:1065)
  [PER got  1<=1014 bits => span 1 +10[3..1016]:26 (1013) => 0x1] (./asn_bit_data.c:132)
  [PER got  7<=1013 bits => span 8 +10[10..1016]:26 (1006) => 0x1a] (./asn_bit_data.c:132)
Failed to decode element RasMessage (./constr_CHOICE.c:882)
Freeing RasMessage as CHOICE (./constr_CHOICE.c:1065)
  [PER got  1<=1013 bits => span 1 +10[4..1016]:26 (1012) => 0x0] (./asn_bit_data.c:132)
  [PER got  5<=1012 bits => span 6 +10[9..1016]:26 (1007) => 0xd] (./asn_bit_data.c:132)
CHOICE RasMessage got index 13 in range 5 (./constr_CHOICE.c:871)
Discovered CHOICE RasMessage encodes bandwidthConfirm (./constr_CHOICE.c:903)
Decoding BandwidthConfirm as SEQUENCE (UPER) (./constr_SEQUENCE.c:1098)
  [PER got  1<=1007 bits => span 7 +11[2..1008]:90 (1006) => 0x0] (./asn_bit_data.c:132)
  [PER got  1<=1006 bits => span 8 +11[3..1008]:90 (1005) => 0x0] (./asn_bit_data.c:132)
Read in presence bitmap for BandwidthConfirm of 1 bits (0..) (./constr_SEQUENCE.c:1120)
Decoding member "requestSeqNum" in BandwidthConfirm (./constr_SEQUENCE.c:1170)
Decoding NativeInteger RequestSeqNum (UPER) (./NativeInteger.c:268)
Integer with range 16 bits (./INTEGER.c:629)
  [PER got 16<=1005 bits => span 24 +11[19..1008]:90 (989) => 0x82fa] (./asn_bit_data.c:132)
Got value 33530 + low 1 (./INTEGER.c:650)
NativeInteger RequestSeqNum got value 33531 (./NativeInteger.c:284)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:125)
Decoding member "bandWidth" in BandwidthConfirm (./constr_SEQUENCE.c:1170)
Decoding NativeInteger BandWidth (UPER) (./NativeInteger.c:268)
Integer with range 32 bits (./INTEGER.c:629)
  [PER got  7<=989 bits => span 31 +13[10..992]:59 (982) => 0x64] (./asn_bit_data.c:132)
  [PER got 24<=982 bits => span 55 +14[26..984]:03 (958) => 0xf0000] (./asn_bit_data.c:132)
  [PER got 31<=989 bits => span 55 +13[34..992]:59 (958) => 0x640f0000] (./asn_bit_data.c:132)
  [PER got  1<=958 bits => span 56 +1[3..960]:35 (957) => 0x1] (./asn_bit_data.c:132)
Got value 3357409281 + low 0 (./INTEGER.c:639)
NativeInteger BandWidth got value 3357409281 (./NativeInteger.c:284)
Freeing INTEGER as a primitive type (./asn_codecs_prim.c:125)
  [PER got  1<= 1 bits => span 1 +0[1..1]:00 (0) => 0x0] (./asn_bit_data.c:132)
Member BandwidthConfirm->nonStandardData is optional, p=0 (1->1) (./constr_SEQUENCE.c:1150)
PER decoding consumed 56, counted 56 (./per_decoder.c:88)
H225RAS: UPER decode successful with 3 skip bits
=== H.225 RAS Message Details ===
RAS Message Type: Bandwidth Confirm
=================================
Freeing RasMessage as CHOICE (./constr_CHOICE.c:1065)
Freeing BandwidthConfirm as SEQUENCE (./constr_SEQUENCE.c:994)
Freeing RequestSeqNum as INTEGER (1, 0x1481b518, Native) (./NativeInteger.c:362)
Freeing BandWidth as INTEGER (1, 0x1481b520, Native) (./NativeInteger.c:362)
