-- H323-MESSAGES.asn
--
-- Taken from ITU ASN.1 database
-- http://www.itu.int/ITU-T/formal-language/itu-t/h/h225-0/2009/H323-MESSAGES.asn
--

-- Module H323-MESSAGES (H.225.0:12/2009)
H323-MESSAGES {itu-t(0) recommendation(0) h(8) h225-0(2250) version(0)
  7 h323-messages(0)} DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

IMPORTS
  SIGNED{}, ENCRYPTED{}, HASHED{}, ChallengeString, TimeStamp, RandomVal,
    Password, EncodedPwdCertToken, ClearToken, CryptoToken,
    AuthenticationMechanism
    FROM H235-SECURITY-MESSAGES
  DataProtocolCapability, T38FaxProfile, QOSCapability
    FROM MULTIMEDIA-SYSTEM-CONTROL {itu-t(0) recommendation(0) h(8) h245(245)
      version(0) 15 multimedia-system-control(0)};

H323-UserInformation ::=
  SEQUENCE -- root for all H.225.0 call signalling messages
   {
  h323-uu-pdu  H323-UU-PDU,
  user-data
    SEQUENCE {protocol-discriminator  INTEGER(0..255),
              user-information        OCTET STRING(SIZE (1..131)),
              ...} OPTIONAL,
  ...
}

H323-UU-PDU ::= SEQUENCE {
  h323-message-body
    CHOICE {setup             Setup-UUIE,
            callProceeding    CallProceeding-UUIE,
            connect           Connect-UUIE,
            alerting          Alerting-UUIE,
            information       Information-UUIE,
            releaseComplete   ReleaseComplete-UUIE,
            facility          Facility-UUIE,
            ...,
            progress          Progress-UUIE,
            empty             NULL, -- used when a Facility message is sent,--
            -- but the Facility-UUIE is not to be invoked
            -- (possible when transporting supplementary
            -- services messages in versions prior to
            -- H.225.0 version 4)
            status            Status-UUIE,
            statusInquiry     StatusInquiry-UUIE,
            setupAcknowledge  SetupAcknowledge-UUIE,
            notify            Notify-UUIE},
  nonStandardData                     NonStandardParameterH225 OPTIONAL,
  ...,
  h4501SupplementaryService           SEQUENCE OF OCTET STRING OPTIONAL,
  -- each sequence of octet string is defined as one
  -- H4501SupplementaryService APDU as defined in
  -- Table 3/H.450.1
  h245Tunnelling                      BOOLEAN,
  -- if TRUE, tunnelling of H.245 messages is enabled
  h245Control                         SEQUENCE OF OCTET STRING OPTIONAL,
  nonStandardControl                  SEQUENCE OF NonStandardParameterH225 OPTIONAL,
  callLinkage                         CallLinkage OPTIONAL,
  tunnelledSignallingMessage
    SEQUENCE {tunnelledProtocolID  TunnelledProtocol, -- tunnelled signalling--
              -- protocol ID
              messageContent       SEQUENCE OF OCTET STRING, -- sequence of entire --
              -- message(s)
              tunnellingRequired   NULL OPTIONAL,
              nonStandardData      NonStandardParameterH225 OPTIONAL,
              ...} OPTIONAL,
  provisionalRespToH245Tunnelling     NULL OPTIONAL,
  stimulusControl                     StimulusControl OPTIONAL,
  genericData                         SEQUENCE OF GenericData OPTIONAL
}

StimulusControl ::= SEQUENCE {
  nonStandard  NonStandardParameterH225 OPTIONAL,
  isText       NULL OPTIONAL,
  h248Message  OCTET STRING OPTIONAL,
  ...
}

Alerting-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  destinationInfo           EndpointType,
  h245Address               TransportAddress OPTIONAL,
  ...,
  callIdentifier            CallIdentifier,
  h245SecurityMode          H245Security OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart                 SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls             BOOLEAN,
  maintainConnection        BOOLEAN,
  alertingAddress           SEQUENCE OF AliasAddress OPTIONAL,
  presentationIndicator     PresentationIndicator OPTIONAL,
  screeningIndicator        ScreeningIndicator OPTIONAL,
  fastConnectRefused        NULL OPTIONAL,
  serviceControl            SEQUENCE OF ServiceControlSession OPTIONAL,
  capacity                  CallCapacity OPTIONAL,
  featureSet                FeatureSet OPTIONAL,
  displayName               SEQUENCE OF DisplayName OPTIONAL
}

CallProceeding-UUIE ::= SEQUENCE {
  protocolIdentifier     ProtocolIdentifier,
  destinationInfo        EndpointType,
  h245Address            TransportAddress OPTIONAL,
  ...,
  callIdentifier         CallIdentifier,
  h245SecurityMode       H245Security OPTIONAL,
  tokens                 SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens           SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart              SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls          BOOLEAN,
  maintainConnection     BOOLEAN,
  fastConnectRefused     NULL OPTIONAL,
  featureSet             FeatureSet OPTIONAL
}

Connect-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  h245Address               TransportAddress OPTIONAL,
  destinationInfo           EndpointType,
  conferenceID              ConferenceIdentifier,
  ...,
  callIdentifier            CallIdentifier,
  h245SecurityMode          H245Security OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart                 SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls             BOOLEAN,
  maintainConnection        BOOLEAN,
  language                  SEQUENCE OF IA5String(SIZE (1..32)) OPTIONAL, -- RFC 1766 language tag
  connectedAddress          SEQUENCE OF AliasAddress OPTIONAL,
  presentationIndicator     PresentationIndicator OPTIONAL,
  screeningIndicator        ScreeningIndicator OPTIONAL,
  fastConnectRefused        NULL OPTIONAL,
  serviceControl            SEQUENCE OF ServiceControlSession OPTIONAL,
  capacity                  CallCapacity OPTIONAL,
  featureSet                FeatureSet OPTIONAL,
  displayName               SEQUENCE OF DisplayName OPTIONAL
}

Information-UUIE ::= SEQUENCE {
  protocolIdentifier     ProtocolIdentifier,
  ...,
  callIdentifier         CallIdentifier,
  tokens                 SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens           SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart              SEQUENCE OF OCTET STRING OPTIONAL,
  fastConnectRefused     NULL OPTIONAL,
  circuitInfo            CircuitInfo OPTIONAL
}

ReleaseComplete-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  reason                    ReleaseCompleteReason OPTIONAL,
  ...,
  callIdentifier            CallIdentifier,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  busyAddress               SEQUENCE OF AliasAddress OPTIONAL,
  presentationIndicator     PresentationIndicator OPTIONAL,
  screeningIndicator        ScreeningIndicator OPTIONAL,
  capacity                  CallCapacity OPTIONAL,
  serviceControl            SEQUENCE OF ServiceControlSession OPTIONAL,
  featureSet                FeatureSet OPTIONAL,
  destinationInfo           EndpointType OPTIONAL,
  displayName               SEQUENCE OF DisplayName OPTIONAL
}

ReleaseCompleteReason ::= CHOICE {
  noBandwidth                  NULL, -- bandwidth taken away or ARQ denied
  gatekeeperResources          NULL, -- exhausted
  unreachableDestination       NULL, -- no transport path to the destination
  destinationRejection         NULL, -- rejected at destination
  invalidRevision              NULL,
  noPermission                 NULL, -- called party's gatekeeper rejects
  unreachableGatekeeper        NULL, -- terminal cannot reach gatekeeper

  -- for ARQ
  gatewayResources             NULL,
  badFormatAddress             NULL,
  adaptiveBusy                 NULL, -- call is dropping due to LAN crowding
  inConf                       NULL, -- called party busy
  undefinedReason              NULL,
  ...,
  facilityCallDeflection       NULL, -- call was deflected using a Facility

  -- message
  securityDenied               NULL, -- incompatible security settings
  calledPartyNotRegistered     NULL, -- used by gatekeeper when endpoint has

  -- preGrantedARQ to bypass ARQ/ACF
  callerNotRegistered          NULL, -- used by gatekeeper when endpoint has

  -- preGrantedARQ to bypass ARQ/ACF
  newConnectionNeeded          NULL, -- indicates that the Setup was not

  -- accepted on this connection, but that
  -- the Setup may be accepted on
  -- a new connection
  nonStandardReason            NonStandardParameterH225,
  replaceWithConferenceInvite  ConferenceIdentifier, -- call dropped due to

  -- subsequent invitation
  -- to a conference
  -- (see *******/H.323)
  genericDataReason            NULL,
  neededFeatureNotSupported    NULL,
  tunnelledSignallingRejected  NULL,
  invalidCID                   NULL,
  securityError                SecurityErrors,
  hopCountExceeded             NULL
}

Setup-UUIE ::= SEQUENCE {
  protocolIdentifier             ProtocolIdentifier,
  h245Address                    TransportAddress OPTIONAL,
  sourceAddress                  SEQUENCE OF AliasAddress OPTIONAL,
  sourceInfo                     EndpointType,
  destinationAddress             SEQUENCE OF AliasAddress OPTIONAL,
  destCallSignalAddress          TransportAddress OPTIONAL,
  destExtraCallInfo              SEQUENCE OF AliasAddress OPTIONAL,
  destExtraCRV                   SEQUENCE OF CallReferenceValue OPTIONAL,
  activeMC                       BOOLEAN,
  conferenceID                   ConferenceIdentifier,
  conferenceGoal
    CHOICE {create                               NULL,
            join                                 NULL,
            invite                               NULL,
            ...,
            capability-negotiation               NULL,
            callIndependentSupplementaryService  NULL},
  callServices                   QseriesOptions OPTIONAL,
  callType                       CallType,
  ...,
  sourceCallSignalAddress        TransportAddress OPTIONAL,
  remoteExtensionAddress         AliasAddress OPTIONAL,
  callIdentifier                 CallIdentifier,
  h245SecurityCapability         SEQUENCE OF H245Security OPTIONAL,
  tokens                         SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                   SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart                      SEQUENCE OF OCTET STRING OPTIONAL,
  mediaWaitForConnect            BOOLEAN,
  canOverlapSend                 BOOLEAN,
  endpointIdentifier             EndpointIdentifier OPTIONAL,
  multipleCalls                  BOOLEAN,
  maintainConnection             BOOLEAN,
  connectionParameters
    SEQUENCE-- additional gateway parameters-- {connectionType
                                                  ScnConnectionType,
                                                numberOfScnConnections
                                                  INTEGER(0..65535),
                                                connectionAggregation
                                                  ScnConnectionAggregation,
                                                ...} OPTIONAL,
  language                       SEQUENCE OF IA5String(SIZE (1..32)) OPTIONAL,
  -- RFC 1766 language tag
  presentationIndicator          PresentationIndicator OPTIONAL,
  screeningIndicator             ScreeningIndicator OPTIONAL,
  serviceControl                 SEQUENCE OF ServiceControlSession OPTIONAL,
  symmetricOperationRequired     NULL OPTIONAL,
  capacity                       CallCapacity OPTIONAL,
  circuitInfo                    CircuitInfo OPTIONAL,
  desiredProtocols               SEQUENCE OF SupportedProtocols OPTIONAL,
  neededFeatures                 SEQUENCE OF FeatureDescriptor OPTIONAL,
  desiredFeatures                SEQUENCE OF FeatureDescriptor OPTIONAL,
  supportedFeatures              SEQUENCE OF FeatureDescriptor OPTIONAL,
  parallelH245Control            SEQUENCE OF OCTET STRING OPTIONAL,
  additionalSourceAddresses      SEQUENCE OF ExtendedAliasAddress OPTIONAL,
  hopCount                       INTEGER(1..31) OPTIONAL,
  displayName                    SEQUENCE OF DisplayName OPTIONAL
}

ScnConnectionType ::= CHOICE {
  unknown     NULL, -- should be selected when connection type is unknown
  bChannel    NULL, -- each individual connection on the SCN is 64 kbit/s.

  -- Note that where SCN delivers 56 kbit/s usable data,
  -- the actual bandwidth allocated on SCN is still
  -- 64 kbit/s.
  hybrid2x64  NULL, -- each connection is a 128 kbit/s hybrid call
  hybrid384   NULL, -- each connection is an H0 (384 kbit/s) hybrid call
  hybrid1536  NULL, -- each connection is an H11 (1536 kbit/s) hybrid call
  hybrid1920  NULL, -- each connection is an H12 (1920 kbit/s) hybrid call
  multirate   NULL, -- bandwidth supplied by SCN using multirate.

  -- In this case, the information transfer rate octet
  -- in the bearer capability shall be set to multirate
  -- and the rate multiplier octet shall denote the
  -- number of B channels.
  ...
}

ScnConnectionAggregation ::= CHOICE {
  auto          NULL, -- aggregation mechanism is unknown
  none          NULL, -- call produced using a single SCN connection
  h221          NULL, -- use H.221 framing to aggregate the connections
  bonded-mode1  NULL, -- use ISO/IEC 13871 bonding mode 1.

  -- Use bonded-mode1 to signal a bonded call if the
  -- precise bonding mode to be used is unknown.
  bonded-mode2  NULL, -- use ISO/IEC 13871 bonding mode 2
  bonded-mode3  NULL, -- use ISO/IEC 13871 bonding mode 3
  ...
}

PresentationIndicator ::= CHOICE {
  presentationAllowed     NULL,
  presentationRestricted  NULL,
  addressNotAvailable     NULL,
  ...
}

ScreeningIndicator ::= ENUMERATED {
  userProvidedNotScreened(0),
  -- number was provided by a remote user
  -- and has not been screened by a gatekeeper
  userProvidedVerifiedAndPassed(1),
  -- number was provided by user
  -- equipment (or by a remote network), and has
  -- been screened by a gatekeeper
  userProvidedVerifiedAndFailed(2),
  -- number was provided by user
  -- equipment (or by a remote network), and the
  -- gatekeeper has determined that the
  -- information is incorrect
  networkProvided(3),
  -- number was provided by a gatekeeper
  ...
  }

Facility-UUIE ::= SEQUENCE {
  protocolIdentifier         ProtocolIdentifier,
  alternativeAddress         TransportAddress OPTIONAL,
  alternativeAliasAddress    SEQUENCE OF AliasAddress OPTIONAL,
  conferenceID               ConferenceIdentifier OPTIONAL,
  reason                     FacilityReason,
  ...,
  callIdentifier             CallIdentifier,
  destExtraCallInfo          SEQUENCE OF AliasAddress OPTIONAL,
  remoteExtensionAddress     AliasAddress OPTIONAL,
  tokens                     SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens               SEQUENCE OF CryptoH323Token OPTIONAL,
  conferences                SEQUENCE OF ConferenceList OPTIONAL,
  h245Address                TransportAddress OPTIONAL,
  fastStart                  SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls              BOOLEAN,
  maintainConnection         BOOLEAN,
  fastConnectRefused         NULL OPTIONAL,
  serviceControl             SEQUENCE OF ServiceControlSession OPTIONAL,
  circuitInfo                CircuitInfo OPTIONAL,
  featureSet                 FeatureSet OPTIONAL,
  destinationInfo            EndpointType OPTIONAL,
  h245SecurityMode           H245Security OPTIONAL
}

ConferenceList ::= SEQUENCE {
  conferenceID     ConferenceIdentifier OPTIONAL,
  conferenceAlias  AliasAddress OPTIONAL,
  nonStandardData  NonStandardParameterH225 OPTIONAL,
  ...
}

FacilityReason ::= CHOICE {
  routeCallToGatekeeper   NULL, -- call must use gatekeeper model

  -- gatekeeper is alternativeAddress
  callForwarded           NULL,
  routeCallToMC           NULL,
  undefinedReason         NULL,
  ...,
  conferenceListChoice    NULL,
  startH245               NULL, -- recipient should connect to h245Address
  noH245                  NULL, -- endpoint does not support H.245
  newTokens               NULL,
  featureSetUpdate        NULL,
  forwardedElements       NULL,
  transportedInformation  NULL
}

Progress-UUIE ::= SEQUENCE {
  protocolIdentifier     ProtocolIdentifier,
  destinationInfo        EndpointType,
  h245Address            TransportAddress OPTIONAL,
  callIdentifier         CallIdentifier,
  h245SecurityMode       H245Security OPTIONAL,
  tokens                 SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens           SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart              SEQUENCE OF OCTET STRING OPTIONAL,
  ...,
  multipleCalls          BOOLEAN,
  maintainConnection     BOOLEAN,
  fastConnectRefused     NULL OPTIONAL
}

TransportAddress ::= CHOICE {
  ipAddress
    SEQUENCE {ip    OCTET STRING(SIZE (4)),
              port  INTEGER(0..65535)},
  ipSourceRoute
    SEQUENCE {ip       OCTET STRING(SIZE (4)),
              port     INTEGER(0..65535),
              route    SEQUENCE OF OCTET STRING(SIZE (4)),
              routing  CHOICE {strict  NULL,
                               loose   NULL,
                               ...},
              ...},
  ipxAddress
    SEQUENCE {node    OCTET STRING(SIZE (6)),
              netnum  OCTET STRING(SIZE (4)),
              port    OCTET STRING(SIZE (2))},
  ip6Address
    SEQUENCE {ip    OCTET STRING(SIZE (16)),
              port  INTEGER(0..65535),
              ...},
  netBios             OCTET STRING(SIZE (16)),
  nsap                OCTET STRING(SIZE (1..20)),
  nonStandardAddress  NonStandardParameterH225,
  ...
}

Status-UUIE ::= SEQUENCE {
  protocolIdentifier  ProtocolIdentifier,
  callIdentifier      CallIdentifier,
  tokens              SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens        SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

StatusInquiry-UUIE ::= SEQUENCE {
  protocolIdentifier  ProtocolIdentifier,
  callIdentifier      CallIdentifier,
  tokens              SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens        SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

SetupAcknowledge-UUIE ::= SEQUENCE {
  protocolIdentifier  ProtocolIdentifier,
  callIdentifier      CallIdentifier,
  tokens              SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens        SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

Notify-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  callIdentifier            CallIdentifier,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...,
  connectedAddress          SEQUENCE OF AliasAddress OPTIONAL,
  presentationIndicator     PresentationIndicator OPTIONAL,
  screeningIndicator        ScreeningIndicator OPTIONAL,
  destinationInfo           EndpointType OPTIONAL,
  displayName               SEQUENCE OF DisplayName OPTIONAL
}

-- Beginning of common message elements section
EndpointType ::= SEQUENCE {
  nonStandardData                 NonStandardParameterH225 OPTIONAL,
  vendor                          VendorIdentifier OPTIONAL,
  gatekeeper                      GatekeeperInfo OPTIONAL,
  gateway                         GatewayInfo OPTIONAL,
  mcu                             McuInfo OPTIONAL, -- mc must be set as well
  terminal                        TerminalInfo OPTIONAL,
  mc                              BOOLEAN, -- shall not be set by itself
  undefinedNode                   BOOLEAN,
  ...,
  set                             BIT STRING(SIZE (32)) OPTIONAL,
  -- shall not be used with mc, gatekeeper
  -- code points for the various SET devices
  -- are defined in the respective SET Annexes
  supportedTunnelledProtocols     SEQUENCE OF TunnelledProtocol OPTIONAL
  -- list of supported tunnelled protocols
}

GatewayInfo ::= SEQUENCE {
  protocol         SEQUENCE OF SupportedProtocols OPTIONAL,
  nonStandardData  NonStandardParameterH225 OPTIONAL,
  ...
}

SupportedProtocols ::= CHOICE {
  nonStandardData      NonStandardParameterH225,
  h310                 H310Caps,
  h320                 H320Caps,
  h321                 H321Caps,
  h322                 H322Caps,
  h323                 H323Caps,
  h324                 H324Caps,
  voice                VoiceCaps,
  t120-only            T120OnlyCaps,
  ...,
  nonStandardProtocol  NonStandardProtocol,
  t38FaxAnnexbOnly     T38FaxAnnexbOnlyCaps,
  sip                  SIPCaps
}

H310Caps ::= SEQUENCE {
  nonStandardData        NonStandardParameterH225 OPTIONAL,
  ...,
  dataRatesSupported     SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes      SEQUENCE OF SupportedPrefix
}

H320Caps ::= SEQUENCE {
  nonStandardData        NonStandardParameterH225 OPTIONAL,
  ...,
  dataRatesSupported     SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes      SEQUENCE OF SupportedPrefix
}

H321Caps ::= SEQUENCE {
  nonStandardData        NonStandardParameterH225 OPTIONAL,
  ...,
  dataRatesSupported     SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes      SEQUENCE OF SupportedPrefix
}

H322Caps ::= SEQUENCE {
  nonStandardData        NonStandardParameterH225 OPTIONAL,
  ...,
  dataRatesSupported     SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes      SEQUENCE OF SupportedPrefix
}

H323Caps ::= SEQUENCE {
  nonStandardData        NonStandardParameterH225 OPTIONAL,
  ...,
  dataRatesSupported     SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes      SEQUENCE OF SupportedPrefix
}

H324Caps ::= SEQUENCE {
  nonStandardData        NonStandardParameterH225 OPTIONAL,
  ...,
  dataRatesSupported     SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes      SEQUENCE OF SupportedPrefix
}

VoiceCaps ::= SEQUENCE {
  nonStandardData        NonStandardParameterH225 OPTIONAL,
  ...,
  dataRatesSupported     SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes      SEQUENCE OF SupportedPrefix
}

T120OnlyCaps ::= SEQUENCE {
  nonStandardData        NonStandardParameterH225 OPTIONAL,
  ...,
  dataRatesSupported     SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes      SEQUENCE OF SupportedPrefix
}

NonStandardProtocol ::= SEQUENCE {
  nonStandardData     NonStandardParameterH225 OPTIONAL,
  dataRatesSupported  SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes   SEQUENCE OF SupportedPrefix,
  ...
}

T38FaxAnnexbOnlyCaps ::= SEQUENCE {
  nonStandardData     NonStandardParameterH225 OPTIONAL,
  dataRatesSupported  SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes   SEQUENCE OF SupportedPrefix,
  t38FaxProtocol      DataProtocolCapability,
  t38FaxProfile       T38FaxProfile,
  ...
}

SIPCaps ::= SEQUENCE {
  nonStandardData     NonStandardParameterH225 OPTIONAL,
  dataRatesSupported  SEQUENCE OF DataRate OPTIONAL,
  supportedPrefixes   SEQUENCE OF SupportedPrefix OPTIONAL,
  ...
}

McuInfo ::= SEQUENCE {
  nonStandardData  NonStandardParameterH225 OPTIONAL,
  ...,
  protocol         SEQUENCE OF SupportedProtocols OPTIONAL
}

TerminalInfo ::= SEQUENCE {nonStandardData  NonStandardParameterH225 OPTIONAL,
                           ...
}

GatekeeperInfo ::= SEQUENCE {
  nonStandardData  NonStandardParameterH225 OPTIONAL,
  ...
}

VendorIdentifier ::= SEQUENCE {
  vendor               H221NonStandard,
  productId            OCTET STRING(SIZE (1..256)) OPTIONAL, -- per vendor
  versionId            OCTET STRING(SIZE (1..256)) OPTIONAL, -- per product
  ...,
  enterpriseNumber     OBJECT IDENTIFIER OPTIONAL
}

H221NonStandard ::= SEQUENCE {
  t35CountryCode    INTEGER(0..255),
  t35Extension      INTEGER(0..255),
  manufacturerCode  INTEGER(0..65535),
  ...
}

TunnelledProtocol ::= SEQUENCE {
  id
    CHOICE {tunnelledProtocolObjectID     OBJECT IDENTIFIER,
            tunnelledProtocolAlternateID  TunnelledProtocolAlternateIdentifier,
            ...},
  subIdentifier  IA5String(SIZE (1..64)) OPTIONAL,
  ...
}

TunnelledProtocolAlternateIdentifier ::= SEQUENCE {
  protocolType     IA5String(SIZE (1..64)),
  protocolVariant  IA5String(SIZE (1..64)) OPTIONAL,
  ...
}

NonStandardParameterH225 ::= SEQUENCE {
  nonStandardIdentifier  NonStandardIdentifierH225,
  data                   OCTET STRING
}

NonStandardIdentifierH225 ::= CHOICE {
  object           OBJECT IDENTIFIER,
  h221NonStandard  H221NonStandard,
  ...
}

AliasAddress ::= CHOICE {
  dialledDigits  IA5String(SIZE (1..128))(FROM ("0123456789#*,")),
  h323-ID        BMPString(SIZE (1..256)), -- Basic ISO/IEC 10646 (Unicode)
  ...,
  url-ID         IA5String(SIZE (1..512)), -- URL style address
  transportID    TransportAddress,
  email-ID       IA5String(SIZE (1..512)), -- rfc822-compliant email address
  partyNumber    PartyNumber,
  mobileUIM      MobileUIM,
  isupNumber     IsupNumber
}

AddressPattern ::= CHOICE {
  wildcard  AliasAddress,
  range     SEQUENCE {startOfRange  PartyNumber,
                      endOfRange    PartyNumber},
  ...
}

PartyNumber ::= CHOICE {
  e164Number                   PublicPartyNumber,
  -- the numbering plan is according to
  -- ITUT Recs E.163 and E.164.
  dataPartyNumber              NumberDigits,
  -- not used, value reserved.
  telexPartyNumber             NumberDigits,
  -- not used, value reserved.
  privateNumber                PrivatePartyNumber,
  -- the numbering plan is according to
  -- ISO/IEC 11571.
  nationalStandardPartyNumber  NumberDigits,
  -- not used, value reserved.
  ...
}

PublicPartyNumber ::= SEQUENCE {
  publicTypeOfNumber  PublicTypeOfNumber,
  publicNumberDigits  NumberDigits
}

PrivatePartyNumber ::= SEQUENCE {
  privateTypeOfNumber  PrivateTypeOfNumber,
  privateNumberDigits  NumberDigits
}

NumberDigits ::= IA5String(SIZE (1..128))(FROM ("0123456789#*,"))

DisplayName ::= SEQUENCE {
  language  IA5String OPTIONAL, -- RFC4646 language tag
  name      BMPString(SIZE (1..80))
}

PublicTypeOfNumber ::= CHOICE {
  unknown                NULL,
  -- if used number digits carry prefix
  -- indicating type
  -- of number according to national
  -- recommendations.
  internationalNumber    NULL,
  nationalNumber         NULL,
  networkSpecificNumber  NULL,
  -- not used, value reserved
  subscriberNumber       NULL,
  abbreviatedNumber      NULL,
  -- valid only for called party number at
  -- the outgoing access, network
  -- substitutes
  -- appropriate number.
  ...
}

PrivateTypeOfNumber ::= CHOICE {
  unknown               NULL,
  level2RegionalNumber  NULL,
  level1RegionalNumber  NULL,
  pISNSpecificNumber    NULL,
  localNumber           NULL,
  abbreviatedNumber     NULL,
  ...
}

MobileUIM ::= CHOICE {
  ansi-41-uim  ANSI-41-UIM, -- Americas standards Wireless Networks
  gsm-uim      GSM-UIM, -- European standards Wireless Networks
  ...
}

TBCD-STRING ::= IA5String(FROM ("0123456789#*abc"))

ANSI-41-UIM ::= SEQUENCE {
  imsi                          TBCD-STRING(SIZE (3..16)) OPTIONAL,
  min                           TBCD-STRING(SIZE (3..16)) OPTIONAL,
  mdn                           TBCD-STRING(SIZE (3..16)) OPTIONAL,
  msisdn                        TBCD-STRING(SIZE (3..16)) OPTIONAL,
  esn                           TBCD-STRING(SIZE (16)) OPTIONAL,
  mscid                         TBCD-STRING(SIZE (3..16)) OPTIONAL,
  system-id
    CHOICE {sid  TBCD-STRING(SIZE (1..4)),
            mid  TBCD-STRING(SIZE (1..4)),
            ...},
  systemMyTypeCode              OCTET STRING(SIZE (1)) OPTIONAL,
  systemAccessType              OCTET STRING(SIZE (1)) OPTIONAL,
  qualificationInformationCode  OCTET STRING(SIZE (1)) OPTIONAL,
  sesn                          TBCD-STRING(SIZE (16)) OPTIONAL,
  soc                           TBCD-STRING(SIZE (3..16)) OPTIONAL,
  ...
  -- IMSI refers to International Mobile Station Identification
  -- MIN refers to Mobile Identification Number
  -- MDN refers to Mobile Directory Number
  -- MSISDN refers to Mobile Station ISDN number
  -- ESN Refers to Electronic Serial Number
  -- MSCID refers to Mobile Switching Center number + Market ID or System ID
  -- SID refers to System Identification and MID refers to Market
  -- Identification
  -- SystemMyTypeCode refers to vendor identification number
  -- SystemAccessType refers to the system access type like power down
  -- registration or call
  -- origination or Short Message response etc.
  -- Qualification Information Code refers to the validity
  -- SESN Refers to SIM Electronic Serial Number for Security purposes of
  -- User Identification
  -- SOC refers to System Operator Code
}

GSM-UIM ::= SEQUENCE {
  imsi    TBCD-STRING(SIZE (3..16)) OPTIONAL,
  tmsi    OCTET STRING(SIZE (1..4)) OPTIONAL,
  msisdn  TBCD-STRING(SIZE (3..16)) OPTIONAL,
  imei    TBCD-STRING(SIZE (15..16)) OPTIONAL,
  hplmn   TBCD-STRING(SIZE (1..4)) OPTIONAL,
  vplmn   TBCD-STRING(SIZE (1..4)) OPTIONAL,
  -- IMSI refers to International Mobile Station Identification
  -- MSISDN refers to Mobile Station ISDN number
  -- IMEI Refers to International Mobile Equipment Identification
  -- VPLMN or HPLMN refers to Visiting or Home Public Land Mobile Network
  -- number
  ...
}

IsupNumber ::= CHOICE {
  e164Number                   IsupPublicPartyNumber,
  -- the numbering plan is according to
  -- ITUT Recs E.163 and E.164.
  dataPartyNumber              IsupDigits, -- not used, value reserved.
  telexPartyNumber             IsupDigits, -- not used, value reserved.
  privateNumber                IsupPrivatePartyNumber,
  -- the numbering plan is according to
  -- ISO/IEC 11571.
  nationalStandardPartyNumber  IsupDigits, -- not used, value reserved.
  ...
}

IsupPublicPartyNumber ::= SEQUENCE {
  natureOfAddress  NatureOfAddress,
  address          IsupDigits,
  ...
}

IsupPrivatePartyNumber ::= SEQUENCE {
  privateTypeOfNumber  PrivateTypeOfNumber,
  address              IsupDigits,
  ...
}

NatureOfAddress ::= CHOICE {
  unknown                                 NULL,
  subscriberNumber                        NULL,
  nationalNumber                          NULL,
  internationalNumber                     NULL,
  networkSpecificNumber                   NULL,
  routingNumberNationalFormat             NULL,
  routingNumberNetworkSpecificFormat      NULL,
  routingNumberWithCalledDirectoryNumber  NULL,
  ...
}

IsupDigits ::= IA5String(SIZE (1..128))(FROM ("0123456789ABCDE"))

ExtendedAliasAddress ::= SEQUENCE {
  address                AliasAddress,
  presentationIndicator  PresentationIndicator OPTIONAL,
  screeningIndicator     ScreeningIndicator OPTIONAL,
  ...
}

Endpoint ::= SEQUENCE {
  nonStandardData                 NonStandardParameterH225 OPTIONAL,
  aliasAddress                    SEQUENCE OF AliasAddress OPTIONAL,
  callSignalAddress               SEQUENCE OF TransportAddress OPTIONAL,
  rasAddress                      SEQUENCE OF TransportAddress OPTIONAL,
  endpointType                    EndpointType OPTIONAL,
  tokens                          SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                    SEQUENCE OF CryptoH323Token OPTIONAL,
  priority                        INTEGER(0..127) OPTIONAL,
  remoteExtensionAddress          SEQUENCE OF AliasAddress OPTIONAL,
  destExtraCallInfo               SEQUENCE OF AliasAddress OPTIONAL,
  ...,
  alternateTransportAddresses     AlternateTransportAddresses OPTIONAL,
  circuitInfo                     CircuitInfo OPTIONAL,
  featureSet                      FeatureSet OPTIONAL
}

AlternateTransportAddresses ::= SEQUENCE {
  annexE   SEQUENCE OF TransportAddress OPTIONAL,
  ...,
  sctp     SEQUENCE OF TransportAddress OPTIONAL
}

UseSpecifiedTransport ::= CHOICE {tcp     NULL,
                                  annexE  NULL,
                                  ...,
                                  sctp    NULL
}

AlternateGK ::= SEQUENCE {
  rasAddress            TransportAddress,
  gatekeeperIdentifier  GatekeeperIdentifier OPTIONAL,
  needToRegister        BOOLEAN,
  priority              INTEGER(0..127),
  ...
}

AltGKInfo ::= SEQUENCE {
  alternateGatekeeper  SEQUENCE OF AlternateGK,
  altGKisPermanent     BOOLEAN,
  ...
}

SecurityServiceMode ::= CHOICE {
  nonStandard  NonStandardParameterH225,
  none         NULL,
  default      NULL,
  ... -- can be extended with other specific modes
}

SecurityCapabilities ::= SEQUENCE {
  nonStandard    NonStandardParameterH225 OPTIONAL,
  encryption     SecurityServiceMode,
  authenticaton  SecurityServiceMode,
  integrity      SecurityServiceMode,
  ...
}

SecurityErrors ::= CHOICE {
  securityWrongSyncTime                 NULL, -- either time server

  -- problem or network delay
  securityReplay                        NULL, -- replay attack encountered
  securityWrongGeneralID                NULL, -- wrong general ID
  securityWrongSendersID                NULL, -- wrong senders ID
  securityIntegrityFailed               NULL, -- integrity check failed
  securityWrongOID                      NULL, -- wrong token OIDs or crypto alg

  -- OIDs
  securityDHmismatch                    NULL, -- mismatch of DH parameters
  securityCertificateExpired            NULL, -- certificate has expired
  securityCertificateDateInvalid        NULL, -- certificate is not yet valid
  securityCertificateRevoked            NULL, -- certificate was found revoked
  securityCertificateNotReadable        NULL, -- decoding error
  securityCertificateSignatureInvalid   NULL, -- wrong signature in the

  -- certificate
  securityCertificateMissing            NULL, -- no certificate available
  securityCertificateIncomplete         NULL, -- missing expected certificate

  -- extensions
  securityUnsupportedCertificateAlgOID  NULL, -- crypto algs not understood
  securityUnknownCA                     NULL, -- CA/root certificate could not

  -- be found
  ...
}

SecurityErrors2 ::= CHOICE {
  securityWrongSyncTime    NULL, -- either time server problem or network

  -- delay
  securityReplay           NULL, -- replay attack encountered
  securityWrongGeneralID   NULL, -- wrong general ID
  securityWrongSendersID   NULL, -- wrong senders ID
  securityIntegrityFailed  NULL, -- integrity check failed
  securityWrongOID         NULL, -- wrong token OIDs or crypto alg OIDs
  ...
}

H245Security ::= CHOICE {
  nonStandard  NonStandardParameterH225,
  noSecurity   NULL,
  tls          SecurityCapabilities,
  ipsec        SecurityCapabilities,
  ...
}

QseriesOptions ::= SEQUENCE {
  q932Full  BOOLEAN, -- if true, indicates full support for Q.932
  q951Full  BOOLEAN, -- if true, indicates full support for Q.951
  q952Full  BOOLEAN, -- if true, indicates full support for Q.952
  q953Full  BOOLEAN, -- if true, indicates full support for Q.953
  q955Full  BOOLEAN, -- if true, indicates full support for Q.955
  q956Full  BOOLEAN, -- if true, indicates full support for Q.956
  q957Full  BOOLEAN, -- if true, indicates full support for Q.957
  q954Info  Q954Details,
  ...
}

Q954Details ::= SEQUENCE {
  conferenceCalling  BOOLEAN,
  threePartyService  BOOLEAN,
  ...
}

GloballyUniqueID ::= OCTET STRING(SIZE (16))

ConferenceIdentifier ::= GloballyUniqueID

RequestSeqNum ::= INTEGER(1..65535)

GatekeeperIdentifier ::= BMPString(SIZE (1..128))

BandWidth ::= INTEGER(0..4294967295) -- in 100s of bits


CallReferenceValue ::= INTEGER(0..65535)

EndpointIdentifier ::= OCTET STRING(SIZE (1..128))

ProtocolIdentifier ::= OBJECT IDENTIFIER

TimeToLive ::= INTEGER(1..4294967295) -- in seconds


H248PackagesDescriptor ::= OCTET STRING -- This octet string contains ASN.1

-- PER encoded H.248
-- PackagesDescriptor
H248SignalsDescriptor ::= OCTET STRING -- This octet string contains

-- ASN.1 PER encoded H.248
-- SignalsDescriptor.
FeatureDescriptor ::=
  GenericData

CallIdentifier ::= SEQUENCE {guid  GloballyUniqueID,
                             ...
}

EncryptIntAlg ::=
  CHOICE { -- core encryption algorithms for RAS message integrity
  nonStandard   NonStandardParameterH225,
  isoAlgorithm  OBJECT IDENTIFIER, -- defined in ISO/IEC 9979
  ...
}

NonIsoIntegrityMechanism ::=
  CHOICE { -- HMAC mechanism used, no truncation, tagging may be necessary!
  hMAC-MD5           NULL,
  hMAC-iso10118-2-s  EncryptIntAlg, -- according to ISO/IEC 10118-2 using

  -- EncryptIntAlg as core block
  -- encryption algorithm (short MAC)
  hMAC-iso10118-2-l  EncryptIntAlg, -- according to ISO/IEC 10118-2 using

  -- EncryptIntAlg as core block
  -- encryption algorithm (long MAC)
  hMAC-iso10118-3    OBJECT IDENTIFIER, -- according to ISO/IEC 10118-3 using

  -- OID as hash function (OID is
  -- SHA-1,
  -- RIPE-MD160,
  -- RIPE-MD128)
  ...
}

IntegrityMechanism ::= CHOICE { -- for RAS message integrity
  nonStandard  NonStandardParameterH225,
  digSig       NULL, -- indicates to apply a digital signature
  iso9797      OBJECT IDENTIFIER, -- according to ISO/IEC 9797 using OID as

  -- core encryption algorithm (X-CBC MAC)
  nonIsoIM     NonIsoIntegrityMechanism,
  ...
}

ICV ::= SEQUENCE {
  algorithmOID  OBJECT IDENTIFIER, -- the algorithm used to compute the

  -- signature
  icv           BIT STRING-- the computed cryptographic --
  -- integrity check value or signature
}

FastStartToken ::=
  ClearToken
    (WITH COMPONENTS {
       ...,
       timeStamp  PRESENT,
       dhkey      PRESENT,
       generalID  PRESENT

     -- set to "alias" --})

EncodedFastStartToken ::= TYPE-IDENTIFIER.&Type(FastStartToken)

CryptoH323Token ::= CHOICE {
  cryptoEPPwdHash
    SEQUENCE {alias      AliasAddress, -- alias of entity generating hash--
              timeStamp  TimeStamp, -- timestamp used in hash--
              token
                HASHED{EncodedPwdCertToken-- generalID set to --
                      -- "alias" -- }},
  cryptoGKPwdHash
    SEQUENCE {gatekeeperId  GatekeeperIdentifier, -- GatekeeperID of GK generating --
              -- hash
              timeStamp     TimeStamp, -- timestamp used in hash--
              token
                HASHED{EncodedPwdCertToken-- generalID set to --
                      -- Gatekeeperid -- }},
  cryptoEPPwdEncr
    ENCRYPTED{EncodedPwdCertToken-- generalID set to --
             -- Gatekeeperid -- },
  cryptoGKPwdEncr
    ENCRYPTED{EncodedPwdCertToken-- generalID set to --
             -- Gatekeeperid -- },
  cryptoEPCert
    SIGNED{EncodedPwdCertToken-- generalID set to --
          -- Gatekeeperid -- },
  cryptoGKCert       SIGNED{EncodedPwdCertToken-- generalID set to alias -- },
  cryptoFastStart    SIGNED{EncodedFastStartToken},
  nestedcryptoToken  CryptoToken,
  ...
}

DataRate ::= SEQUENCE {
  nonStandardData    NonStandardParameterH225 OPTIONAL,
  channelRate        BandWidth,
  channelMultiplier  INTEGER(1..256) OPTIONAL,
  ...
}

CallLinkage ::= SEQUENCE {
  globalCallId  GloballyUniqueID OPTIONAL,
  threadId      GloballyUniqueID OPTIONAL,
  ...
}

SupportedPrefix ::= SEQUENCE {
  nonStandardData  NonStandardParameterH225 OPTIONAL,
  prefix           AliasAddress,
  ...
}

CapacityReportingCapability ::= SEQUENCE {canReportCallCapacity  BOOLEAN,
                                          ...
}

CapacityReportingSpecification ::= SEQUENCE {
  when  SEQUENCE {callStart  NULL OPTIONAL,
                  callEnd    NULL OPTIONAL,
                  ...},
  ...
}

CallCapacity ::= SEQUENCE {
  maximumCallCapacity  CallCapacityInfo OPTIONAL,
  currentCallCapacity  CallCapacityInfo OPTIONAL,
  ...
}

CallCapacityInfo ::= SEQUENCE {
  voiceGwCallsAvailable             SEQUENCE OF CallsAvailable OPTIONAL,
  h310GwCallsAvailable              SEQUENCE OF CallsAvailable OPTIONAL,
  h320GwCallsAvailable              SEQUENCE OF CallsAvailable OPTIONAL,
  h321GwCallsAvailable              SEQUENCE OF CallsAvailable OPTIONAL,
  h322GwCallsAvailable              SEQUENCE OF CallsAvailable OPTIONAL,
  h323GwCallsAvailable              SEQUENCE OF CallsAvailable OPTIONAL,
  h324GwCallsAvailable              SEQUENCE OF CallsAvailable OPTIONAL,
  t120OnlyGwCallsAvailable          SEQUENCE OF CallsAvailable OPTIONAL,
  t38FaxAnnexbOnlyGwCallsAvailable  SEQUENCE OF CallsAvailable OPTIONAL,
  terminalCallsAvailable            SEQUENCE OF CallsAvailable OPTIONAL,
  mcuCallsAvailable                 SEQUENCE OF CallsAvailable OPTIONAL,
  ...,
  sipGwCallsAvailable               SEQUENCE OF CallsAvailable OPTIONAL
}

CallsAvailable ::= SEQUENCE {
  calls       INTEGER(0..4294967295),
  group       IA5String(SIZE (1..128)) OPTIONAL,
  ...,
  carrier     CarrierInfo OPTIONAL
}

CircuitInfo ::= SEQUENCE {
  sourceCircuitID       CircuitIdentifier OPTIONAL,
  destinationCircuitID  CircuitIdentifier OPTIONAL,
  genericData           SEQUENCE OF GenericData OPTIONAL,
  ...
}

CircuitIdentifier ::= SEQUENCE {
  cic         CicInfo OPTIONAL,
  group       GroupID OPTIONAL,
  ...,
  carrier     CarrierInfo OPTIONAL
}

CicInfo ::= SEQUENCE {
  cic        SEQUENCE OF OCTET STRING(SIZE (2..4)),
  pointCode  OCTET STRING(SIZE (2..5)),
  ...
}

GroupID ::= SEQUENCE {
  member  SEQUENCE OF INTEGER(0..65535) OPTIONAL,
  group   IA5String(SIZE (1..128)),
  ...
}

CarrierInfo ::= SEQUENCE {
  carrierIdentificationCode  OCTET STRING(SIZE (3..4)) OPTIONAL,
  carrierName                IA5String(SIZE (1..128)) OPTIONAL,
  ...
}

ServiceControlDescriptor ::= CHOICE {
  url                       IA5String(SIZE (0..512)), -- indicates a URL-

  -- referenced
  -- protocol/resource
  signal                    H248SignalsDescriptor,
  nonStandard               NonStandardParameterH225,
  callCreditServiceControl  CallCreditServiceControl,
  ...
}

ServiceControlSession ::= SEQUENCE {
  sessionId  INTEGER(0..255),
  contents   ServiceControlDescriptor OPTIONAL,
  reason     CHOICE {open     NULL,
                     refresh  NULL,
                     close    NULL,
                     ...},
  ...
}

RasUsageInfoTypes ::= SEQUENCE {
  nonStandardUsageTypes  SEQUENCE OF NonStandardParameterH225,
  startTime              NULL OPTIONAL,
  endTime                NULL OPTIONAL,
  terminationCause       NULL OPTIONAL,
  ...
}

RasUsageSpecification ::= SEQUENCE {
  when
    SEQUENCE {start  NULL OPTIONAL,
              end    NULL OPTIONAL,
              inIrr  NULL OPTIONAL,
              ...},
  callStartingPoint
    SEQUENCE {alerting  NULL OPTIONAL,
              connect   NULL OPTIONAL,
              ...} OPTIONAL,
  required           RasUsageInfoTypes,
  ...
}

RasUsageInformation ::= SEQUENCE {
  nonStandardUsageFields  SEQUENCE OF NonStandardParameterH225,
  alertingTime            TimeStamp OPTIONAL,
  connectTime             TimeStamp OPTIONAL,
  endTime                 TimeStamp OPTIONAL,
  ...
}

CallTerminationCause ::= CHOICE {
  releaseCompleteReason   ReleaseCompleteReason,
  releaseCompleteCauseIE  OCTET STRING(SIZE (2..32)),
  ...
}

BandwidthDetails ::= SEQUENCE {
  sender         BOOLEAN, -- TRUE=sender, FALSE=receiver
  multicast      BOOLEAN, -- TRUE if stream is multicast
  bandwidth      BandWidth, -- Bandwidth used for stream
  rtcpAddresses  TransportChannelInfo, -- RTCP addresses for media stream
  ...
}

CallCreditCapability ::= SEQUENCE {
  canDisplayAmountString   BOOLEAN OPTIONAL,
  canEnforceDurationLimit  BOOLEAN OPTIONAL,
  ...
}

CallCreditServiceControl ::= SEQUENCE {
  amountString              BMPString(SIZE (1..512)) OPTIONAL, -- (Unicode)
  billingMode               CHOICE {credit  NULL,
                                    debit   NULL,
                                    ...} OPTIONAL,
  callDurationLimit         INTEGER(1..4294967295) OPTIONAL, -- in seconds
  enforceCallDurationLimit  BOOLEAN OPTIONAL,
  callStartingPoint         CHOICE {alerting  NULL,
                                    connect   NULL,
                                    ...} OPTIONAL,
  ...
}

GenericData ::= SEQUENCE {
  id          GenericIdentifier,
  parameters  SEQUENCE (SIZE (1..512)) OF EnumeratedParameter OPTIONAL,
  ...
}

GenericIdentifier ::= CHOICE {
  standard     INTEGER(0..16383, ...),
  oid          OBJECT IDENTIFIER,
  nonStandard  GloballyUniqueID,
  ...
}

EnumeratedParameter ::= SEQUENCE {
  id       GenericIdentifier,
  content  Content OPTIONAL,
  ...
}

Content ::= CHOICE {
  raw        OCTET STRING,
  text       IA5String,
  unicode    BMPString,
  bool       BOOLEAN,
  number8    INTEGER(0..255),
  number16   INTEGER(0..65535),
  number32   INTEGER(0..4294967295),
  id         GenericIdentifier,
  alias      AliasAddress,
  transport  TransportAddress,
  compound   SEQUENCE (SIZE (1..512)) OF EnumeratedParameter,
  nested     SEQUENCE (SIZE (1..16)) OF GenericData,
  ...
}

FeatureSet ::= SEQUENCE {
  replacementFeatureSet  BOOLEAN,
  neededFeatures         SEQUENCE OF FeatureDescriptor OPTIONAL,
  desiredFeatures        SEQUENCE OF FeatureDescriptor OPTIONAL,
  supportedFeatures      SEQUENCE OF FeatureDescriptor OPTIONAL,
  ...
}

TransportChannelInfo ::= SEQUENCE {
  sendAddress  TransportAddress OPTIONAL,
  recvAddress  TransportAddress OPTIONAL,
  ...
}

RTPSession ::= SEQUENCE {
  rtpAddress            TransportChannelInfo,
  rtcpAddress           TransportChannelInfo,
  cname                 PrintableString,
  ssrc                  INTEGER(1..4294967295),
  sessionId             INTEGER(1..255),
  associatedSessionIds  SEQUENCE OF INTEGER(1..255),
  ...,
  multicast             NULL OPTIONAL,
  bandwidth             BandWidth OPTIONAL
}

RehomingModel ::= CHOICE {gatekeeperBased  NULL,
                          endpointBased    NULL
}

RasMessage ::= CHOICE {
  gatekeeperRequest           GatekeeperRequest,
  gatekeeperConfirm           GatekeeperConfirm,
  gatekeeperReject            GatekeeperReject,
  registrationRequest         RegistrationRequest,
  registrationConfirm         RegistrationConfirm,
  registrationReject          RegistrationReject,
  unregistrationRequest       UnregistrationRequest,
  unregistrationConfirm       UnregistrationConfirm,
  admissionRequest            AdmissionRequest,
  admissionConfirm            AdmissionConfirm,
  admissionReject             AdmissionReject,
  bandwidthRequest            BandwidthRequest,
  bandwidthConfirm            BandwidthConfirm,
  bandwidthReject             BandwidthReject,
  disengageRequest            DisengageRequest,
  disengageConfirm            DisengageConfirm,
  disengageReject             DisengageReject,
  locationRequest             LocationRequest,
  locationConfirm             LocationConfirm,
  locationReject              LocationReject,
  infoRequest                 InfoRequest,
  infoRequestResponse         InfoRequestResponse,
  nonStandardMessage          NonStandardMessage,
  unknownMessageResponse      UnknownMessageResponse,
  ...,
  requestInProgress           RequestInProgress,
  resourcesAvailableIndicate  ResourcesAvailableIndicate,
  resourcesAvailableConfirm   ResourcesAvailableConfirm,
  infoRequestAck              InfoRequestAck,
  infoRequestNak              InfoRequestNak,
  serviceControlIndication    ServiceControlIndication,
  serviceControlResponse      ServiceControlResponse,
  admissionConfirmSequence    SEQUENCE OF AdmissionConfirm
}

GatekeeperRequest ::= SEQUENCE --(GRQ)
                       {
  requestSeqNum                RequestSeqNum,
  protocolIdentifier           ProtocolIdentifier,
  nonStandardData              NonStandardParameterH225 OPTIONAL,
  rasAddress                   TransportAddress,
  endpointType                 EndpointType,
  gatekeeperIdentifier         GatekeeperIdentifier OPTIONAL,
  callServices                 QseriesOptions OPTIONAL,
  endpointAlias                SEQUENCE OF AliasAddress OPTIONAL,
  ...,
  alternateEndpoints           SEQUENCE OF Endpoint OPTIONAL,
  tokens                       SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                 SEQUENCE OF CryptoH323Token OPTIONAL,
  authenticationCapability     SEQUENCE OF AuthenticationMechanism OPTIONAL,
  algorithmOIDs                SEQUENCE OF OBJECT IDENTIFIER OPTIONAL,
  integrity                    SEQUENCE OF IntegrityMechanism OPTIONAL,
  integrityCheckValue          ICV OPTIONAL,
  supportsAltGK                NULL OPTIONAL,
  featureSet                   FeatureSet OPTIONAL,
  genericData                  SEQUENCE OF GenericData OPTIONAL,
  supportsAssignedGK           BOOLEAN,
  assignedGatekeeper           AlternateGK OPTIONAL
}

GatekeeperConfirm ::= SEQUENCE --(GCF)
                       {
  requestSeqNum           RequestSeqNum,
  protocolIdentifier      ProtocolIdentifier,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  gatekeeperIdentifier    GatekeeperIdentifier OPTIONAL,
  rasAddress              TransportAddress,
  ...,
  alternateGatekeeper     SEQUENCE OF AlternateGK OPTIONAL,
  authenticationMode      AuthenticationMechanism OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  algorithmOID            OBJECT IDENTIFIER OPTIONAL,
  integrity               SEQUENCE OF IntegrityMechanism OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL,
  assignedGatekeeper      AlternateGK OPTIONAL,
  rehomingModel           RehomingModel OPTIONAL
}

GatekeeperReject ::= SEQUENCE --(GRJ)
                      {
  requestSeqNum           RequestSeqNum,
  protocolIdentifier      ProtocolIdentifier,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  gatekeeperIdentifier    GatekeeperIdentifier OPTIONAL,
  rejectReason            GatekeeperRejectReason,
  ...,
  altGKInfo               AltGKInfo OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL
}

GatekeeperRejectReason ::= CHOICE {
  resourceUnavailable        NULL,
  terminalExcluded           NULL, -- permission failure, not a resource

  -- failure
  invalidRevision            NULL,
  undefinedReason            NULL,
  ...,
  securityDenial             NULL,
  genericDataReason          NULL,
  neededFeatureNotSupported  NULL,
  securityError              SecurityErrors
}

RegistrationRequest ::= SEQUENCE --(RRQ)
                         {
  requestSeqNum                   RequestSeqNum,
  protocolIdentifier              ProtocolIdentifier,
  nonStandardData                 NonStandardParameterH225 OPTIONAL,
  discoveryComplete               BOOLEAN,
  callSignalAddress               SEQUENCE OF TransportAddress,
  rasAddress                      SEQUENCE OF TransportAddress,
  terminalType                    EndpointType,
  terminalAlias                   SEQUENCE OF AliasAddress OPTIONAL,
  gatekeeperIdentifier            GatekeeperIdentifier OPTIONAL,
  endpointVendor                  VendorIdentifier,
  ...,
  alternateEndpoints              SEQUENCE OF Endpoint OPTIONAL,
  timeToLive                      TimeToLive OPTIONAL,
  tokens                          SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                    SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue             ICV OPTIONAL,
  keepAlive                       BOOLEAN,
  endpointIdentifier              EndpointIdentifier OPTIONAL,
  willSupplyUUIEs                 BOOLEAN,
  maintainConnection              BOOLEAN,
  alternateTransportAddresses     AlternateTransportAddresses OPTIONAL,
  additiveRegistration            NULL OPTIONAL,
  terminalAliasPattern            SEQUENCE OF AddressPattern OPTIONAL,
  supportsAltGK                   NULL OPTIONAL,
  usageReportingCapability        RasUsageInfoTypes OPTIONAL,
  multipleCalls                   BOOLEAN OPTIONAL,
  supportedH248Packages           SEQUENCE OF H248PackagesDescriptor OPTIONAL,
  callCreditCapability            CallCreditCapability OPTIONAL,
  capacityReportingCapability     CapacityReportingCapability OPTIONAL,
  capacity                        CallCapacity OPTIONAL,
  featureSet                      FeatureSet OPTIONAL,
  genericData                     SEQUENCE OF GenericData OPTIONAL,
  restart                         NULL OPTIONAL,
  supportsACFSequences            NULL OPTIONAL,
  supportsAssignedGK              BOOLEAN,
  assignedGatekeeper              AlternateGK OPTIONAL,
  transportQOS                    TransportQOS OPTIONAL,
  language                        SEQUENCE OF IA5String(SIZE (1..32)) OPTIONAL
}

RegistrationConfirm ::= SEQUENCE --(RCF)
                         {
  requestSeqNum                    RequestSeqNum,
  protocolIdentifier               ProtocolIdentifier,
  nonStandardData                  NonStandardParameterH225 OPTIONAL,
  callSignalAddress                SEQUENCE OF TransportAddress,
  terminalAlias                    SEQUENCE OF AliasAddress OPTIONAL,
  gatekeeperIdentifier             GatekeeperIdentifier OPTIONAL,
  endpointIdentifier               EndpointIdentifier,
  ...,
  alternateGatekeeper              SEQUENCE OF AlternateGK OPTIONAL,
  timeToLive                       TimeToLive OPTIONAL,
  tokens                           SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                     SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue              ICV OPTIONAL,
  willRespondToIRR                 BOOLEAN,
  preGrantedARQ
    SEQUENCE {makeCall                          BOOLEAN,
              useGKCallSignalAddressToMakeCall  BOOLEAN,
              answerCall                        BOOLEAN,
              useGKCallSignalAddressToAnswer    BOOLEAN,
              ...,
              irrFrequencyInCall                INTEGER(1..65535) OPTIONAL, -- in seconds; --
              -- not present
              -- if GK does
              -- not want IRRs
              totalBandwidthRestriction         BandWidth OPTIONAL, -- total limit --
              -- for all
              -- concurrent
              -- calls
              alternateTransportAddresses
                AlternateTransportAddresses OPTIONAL,
              useSpecifiedTransport             UseSpecifiedTransport OPTIONAL
  } OPTIONAL,
  maintainConnection               BOOLEAN,
  serviceControl                   SEQUENCE OF ServiceControlSession OPTIONAL,
  supportsAdditiveRegistration     NULL OPTIONAL,
  terminalAliasPattern             SEQUENCE OF AddressPattern OPTIONAL,
  supportedPrefixes                SEQUENCE OF SupportedPrefix OPTIONAL,
  usageSpec                        SEQUENCE OF RasUsageSpecification OPTIONAL,
  featureServerAlias               AliasAddress OPTIONAL,
  capacityReportingSpec            CapacityReportingSpecification OPTIONAL,
  featureSet                       FeatureSet OPTIONAL,
  genericData                      SEQUENCE OF GenericData OPTIONAL,
  assignedGatekeeper               AlternateGK OPTIONAL,
  rehomingModel                    RehomingModel OPTIONAL,
  transportQOS                     TransportQOS OPTIONAL
}

RegistrationReject ::= SEQUENCE --(RRJ)
                        {
  requestSeqNum           RequestSeqNum,
  protocolIdentifier      ProtocolIdentifier,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  rejectReason            RegistrationRejectReason,
  gatekeeperIdentifier    GatekeeperIdentifier OPTIONAL,
  ...,
  altGKInfo               AltGKInfo OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL,
  assignedGatekeeper      AlternateGK OPTIONAL
}

RegistrationRejectReason ::= CHOICE {
  discoveryRequired                 NULL,
  invalidRevision                   NULL,
  invalidCallSignalAddress          NULL,
  invalidRASAddress                 NULL, -- supplied address is invalid
  duplicateAlias                    SEQUENCE OF AliasAddress,
  -- alias registered to another
  -- endpoint
  invalidTerminalType               NULL,
  undefinedReason                   NULL,
  transportNotSupported             NULL, -- one or more of the transports
  ...,
  transportQOSNotSupported          NULL, -- endpoint QoS not supported
  resourceUnavailable               NULL, -- gatekeeper resources exhausted
  invalidAlias                      NULL, -- alias not consistent with

  -- gatekeeper rules
  securityDenial                    NULL,
  fullRegistrationRequired          NULL, -- registration permission has

  -- expired
  additiveRegistrationNotSupported  NULL,
  invalidTerminalAliases
    SEQUENCE {terminalAlias         SEQUENCE OF AliasAddress OPTIONAL,
              terminalAliasPattern  SEQUENCE OF AddressPattern OPTIONAL,
              supportedPrefixes     SEQUENCE OF SupportedPrefix OPTIONAL,
              ...},
  genericDataReason                 NULL,
  neededFeatureNotSupported         NULL,
  securityError                     SecurityErrors,
  registerWithAssignedGK            NULL
}

UnregistrationRequest ::= SEQUENCE --(URQ)
                           {
  requestSeqNum            RequestSeqNum,
  callSignalAddress        SEQUENCE OF TransportAddress,
  endpointAlias            SEQUENCE OF AliasAddress OPTIONAL,
  nonStandardData          NonStandardParameterH225 OPTIONAL,
  endpointIdentifier       EndpointIdentifier OPTIONAL,
  ...,
  alternateEndpoints       SEQUENCE OF Endpoint OPTIONAL,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  reason                   UnregRequestReason OPTIONAL,
  endpointAliasPattern     SEQUENCE OF AddressPattern OPTIONAL,
  supportedPrefixes        SEQUENCE OF SupportedPrefix OPTIONAL,
  alternateGatekeeper      SEQUENCE OF AlternateGK OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL,
  assignedGatekeeper       AlternateGK OPTIONAL
}

UnregRequestReason ::= CHOICE {
  reregistrationRequired  NULL,
  ttlExpired              NULL,
  securityDenial          NULL,
  undefinedReason         NULL,
  ...,
  maintenance             NULL,
  securityError           SecurityErrors2,
  registerWithAssignedGK  NULL
}

UnregistrationConfirm ::= SEQUENCE --(UCF)
                           {
  requestSeqNum           RequestSeqNum,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  ...,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL,
  assignedGatekeeper      AlternateGK OPTIONAL
}

UnregistrationReject ::= SEQUENCE --(URJ)
                          {
  requestSeqNum           RequestSeqNum,
  rejectReason            UnregRejectReason,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  ...,
  altGKInfo               AltGKInfo OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL
}

UnregRejectReason ::= CHOICE {
  notCurrentlyRegistered  NULL,
  callInProgress          NULL,
  undefinedReason         NULL,
  ...,
  permissionDenied        NULL, -- requesting user not allowed to

  -- unregister specified user
  securityDenial          NULL,
  securityError           SecurityErrors2
}

AdmissionRequest ::= SEQUENCE --(ARQ)
                      {
  requestSeqNum                RequestSeqNum,
  callType                     CallType,
  callModel                    CallModel OPTIONAL,
  endpointIdentifier           EndpointIdentifier,
  destinationInfo              SEQUENCE OF AliasAddress OPTIONAL,
  destCallSignalAddress        TransportAddress OPTIONAL,
  destExtraCallInfo            SEQUENCE OF AliasAddress OPTIONAL,
  srcInfo                      SEQUENCE OF AliasAddress,
  srcCallSignalAddress         TransportAddress OPTIONAL,
  bandWidth                    BandWidth,
  callReferenceValue           CallReferenceValue,
  nonStandardData              NonStandardParameterH225 OPTIONAL,
  callServices                 QseriesOptions OPTIONAL,
  conferenceID                 ConferenceIdentifier,
  activeMC                     BOOLEAN,
  answerCall                   BOOLEAN, -- answering a call
  ...,
  canMapAlias                  BOOLEAN, -- can handle alias address
  callIdentifier               CallIdentifier,
  srcAlternatives              SEQUENCE OF Endpoint OPTIONAL,
  destAlternatives             SEQUENCE OF Endpoint OPTIONAL,
  gatekeeperIdentifier         GatekeeperIdentifier OPTIONAL,
  tokens                       SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                 SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue          ICV OPTIONAL,
  transportQOS                 TransportQOS OPTIONAL,
  willSupplyUUIEs              BOOLEAN,
  callLinkage                  CallLinkage OPTIONAL,
  gatewayDataRate              DataRate OPTIONAL,
  capacity                     CallCapacity OPTIONAL,
  circuitInfo                  CircuitInfo OPTIONAL,
  desiredProtocols             SEQUENCE OF SupportedProtocols OPTIONAL,
  desiredTunnelledProtocol     TunnelledProtocol OPTIONAL,
  featureSet                   FeatureSet OPTIONAL,
  genericData                  SEQUENCE OF GenericData OPTIONAL,
  canMapSrcAlias               BOOLEAN
}

CallType ::= CHOICE {
  pointToPoint  NULL, -- Point-to-point
  oneToN        NULL, -- no interaction (FFS)
  nToOne        NULL, -- no interaction (FFS)
  nToN          NULL, -- interactive (multipoint)
  ...
}

CallModel ::= CHOICE {direct            NULL,
                      gatekeeperRouted  NULL,
                      ...
}

TransportQOS ::= CHOICE {
  endpointControlled    NULL,
  gatekeeperControlled  NULL,
  noControl             NULL,
  ...,
  qOSCapabilities       SEQUENCE SIZE (1..256) OF QOSCapability
}

AdmissionConfirm ::= SEQUENCE --(ACF)
                      {
  requestSeqNum                   RequestSeqNum,
  bandWidth                       BandWidth,
  callModel                       CallModel,
  destCallSignalAddress           TransportAddress,
  irrFrequency                    INTEGER(1..65535) OPTIONAL,
  nonStandardData                 NonStandardParameterH225 OPTIONAL,
  ...,
  destinationInfo                 SEQUENCE OF AliasAddress OPTIONAL,
  destExtraCallInfo               SEQUENCE OF AliasAddress OPTIONAL,
  destinationType                 EndpointType OPTIONAL,
  remoteExtensionAddress          SEQUENCE OF AliasAddress OPTIONAL,
  alternateEndpoints              SEQUENCE OF Endpoint OPTIONAL,
  tokens                          SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                    SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue             ICV OPTIONAL,
  transportQOS                    TransportQOS OPTIONAL,
  willRespondToIRR                BOOLEAN,
  uuiesRequested                  UUIEsRequested,
  language                        SEQUENCE OF IA5String(SIZE (1..32)) OPTIONAL,
  alternateTransportAddresses     AlternateTransportAddresses OPTIONAL,
  useSpecifiedTransport           UseSpecifiedTransport OPTIONAL,
  circuitInfo                     CircuitInfo OPTIONAL,
  usageSpec                       SEQUENCE OF RasUsageSpecification OPTIONAL,
  supportedProtocols              SEQUENCE OF SupportedProtocols OPTIONAL,
  serviceControl                  SEQUENCE OF ServiceControlSession OPTIONAL,
  multipleCalls                   BOOLEAN OPTIONAL,
  featureSet                      FeatureSet OPTIONAL,
  genericData                     SEQUENCE OF GenericData OPTIONAL,
  modifiedSrcInfo                 SEQUENCE OF AliasAddress OPTIONAL,
  assignedGatekeeper              AlternateGK OPTIONAL
}

UUIEsRequested ::= SEQUENCE {
  setup                BOOLEAN,
  callProceeding       BOOLEAN,
  connect              BOOLEAN,
  alerting             BOOLEAN,
  information          BOOLEAN,
  releaseComplete      BOOLEAN,
  facility             BOOLEAN,
  progress             BOOLEAN,
  empty                BOOLEAN,
  ...,
  status               BOOLEAN,
  statusInquiry        BOOLEAN,
  setupAcknowledge     BOOLEAN,
  notify               BOOLEAN
}

AdmissionReject ::= SEQUENCE --(ARJ)
                     {
  requestSeqNum           RequestSeqNum,
  rejectReason            AdmissionRejectReason,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  ...,
  altGKInfo               AltGKInfo OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  callSignalAddress       SEQUENCE OF TransportAddress OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  serviceControl          SEQUENCE OF ServiceControlSession OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL,
  assignedGatekeeper      AlternateGK OPTIONAL
}

AdmissionRejectReason ::= CHOICE {
  calledPartyNotRegistered   NULL, -- cannot translate address
  invalidPermission          NULL, -- permission has expired
  requestDenied              NULL,
  undefinedReason            NULL,
  callerNotRegistered        NULL,
  routeCallToGatekeeper      NULL,
  invalidEndpointIdentifier  NULL,
  resourceUnavailable        NULL,
  ...,
  securityDenial             NULL,
  qosControlNotSupported     NULL,
  incompleteAddress          NULL,
  aliasesInconsistent        NULL, -- multiple aliases in request

  -- identify distinct people
  routeCallToSCN             SEQUENCE OF PartyNumber,
  exceedsCallCapacity        NULL, -- destination does not have the

  -- capacity for this call
  collectDestination         NULL,
  collectPIN                 NULL,
  genericDataReason          NULL,
  neededFeatureNotSupported  NULL,
  securityError              SecurityErrors2,
  securityDHmismatch         NULL, -- mismatch of DH parameters
  noRouteToDestination       NULL, -- destination unreachable
  unallocatedNumber          NULL, -- destination number unassigned
  registerWithAssignedGK     NULL
}

BandwidthRequest ::= SEQUENCE --(BRQ)
                      {
  requestSeqNum            RequestSeqNum,
  endpointIdentifier       EndpointIdentifier,
  conferenceID             ConferenceIdentifier,
  callReferenceValue       CallReferenceValue,
  callType                 CallType OPTIONAL,
  bandWidth                BandWidth,
  nonStandardData          NonStandardParameterH225 OPTIONAL,
  ...,
  callIdentifier           CallIdentifier,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  answeredCall             BOOLEAN,
  callLinkage              CallLinkage OPTIONAL,
  capacity                 CallCapacity OPTIONAL,
  usageInformation         RasUsageInformation OPTIONAL,
  bandwidthDetails         SEQUENCE OF BandwidthDetails OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL,
  transportQOS             TransportQOS OPTIONAL
}

BandwidthConfirm ::= SEQUENCE --(BCF)
                      {
  requestSeqNum           RequestSeqNum,
  bandWidth               BandWidth,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  ...,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  capacity                CallCapacity OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL,
  transportQOS            TransportQOS OPTIONAL
}

BandwidthReject ::= SEQUENCE --(BRJ)
                     {
  requestSeqNum           RequestSeqNum,
  rejectReason            BandRejectReason,
  allowedBandWidth        BandWidth,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  ...,
  altGKInfo               AltGKInfo OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL
}

BandRejectReason ::= CHOICE {
  notBound               NULL, -- discovery permission has aged
  invalidConferenceID    NULL, -- possible revision
  invalidPermission      NULL, -- true permission violation
  insufficientResources  NULL,
  invalidRevision        NULL,
  undefinedReason        NULL,
  ...,
  securityDenial         NULL,
  securityError          SecurityErrors2
}

LocationRequest ::= SEQUENCE --(LRQ)
                     {
  requestSeqNum                RequestSeqNum,
  endpointIdentifier           EndpointIdentifier OPTIONAL,
  destinationInfo              SEQUENCE OF AliasAddress,
  nonStandardData              NonStandardParameterH225 OPTIONAL,
  replyAddress                 TransportAddress,
  ...,
  sourceInfo                   SEQUENCE OF AliasAddress OPTIONAL,
  canMapAlias                  BOOLEAN, -- can handle alias address
  gatekeeperIdentifier         GatekeeperIdentifier OPTIONAL,
  tokens                       SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                 SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue          ICV OPTIONAL,
  desiredProtocols             SEQUENCE OF SupportedProtocols OPTIONAL,
  desiredTunnelledProtocol     TunnelledProtocol OPTIONAL,
  featureSet                   FeatureSet OPTIONAL,
  genericData                  SEQUENCE OF GenericData OPTIONAL,
  hopCount                     INTEGER(1..255) OPTIONAL,
  circuitInfo                  CircuitInfo OPTIONAL,
  callIdentifier               CallIdentifier OPTIONAL,
  bandWidth                    BandWidth OPTIONAL,
  sourceEndpointInfo           SEQUENCE OF AliasAddress OPTIONAL,
  canMapSrcAlias               BOOLEAN,
  language                     SEQUENCE OF IA5String(SIZE (1..32)) OPTIONAL
}

LocationConfirm ::= SEQUENCE --(LCF)
                     {
  requestSeqNum                   RequestSeqNum,
  callSignalAddress               TransportAddress,
  rasAddress                      TransportAddress,
  nonStandardData                 NonStandardParameterH225 OPTIONAL,
  ...,
  destinationInfo                 SEQUENCE OF AliasAddress OPTIONAL,
  destExtraCallInfo               SEQUENCE OF AliasAddress OPTIONAL,
  destinationType                 EndpointType OPTIONAL,
  remoteExtensionAddress          SEQUENCE OF AliasAddress OPTIONAL,
  alternateEndpoints              SEQUENCE OF Endpoint OPTIONAL,
  tokens                          SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                    SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue             ICV OPTIONAL,
  alternateTransportAddresses     AlternateTransportAddresses OPTIONAL,
  supportedProtocols              SEQUENCE OF SupportedProtocols OPTIONAL,
  multipleCalls                   BOOLEAN OPTIONAL,
  featureSet                      FeatureSet OPTIONAL,
  genericData                     SEQUENCE OF GenericData OPTIONAL,
  circuitInfo                     CircuitInfo OPTIONAL,
  serviceControl                  SEQUENCE OF ServiceControlSession OPTIONAL,
  modifiedSrcInfo                 SEQUENCE OF AliasAddress OPTIONAL,
  bandWidth                       BandWidth OPTIONAL
}

LocationReject ::= SEQUENCE --(LRJ)
                    {
  requestSeqNum           RequestSeqNum,
  rejectReason            LocationRejectReason,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  ...,
  altGKInfo               AltGKInfo OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL,
  serviceControl          SEQUENCE OF ServiceControlSession OPTIONAL
}

LocationRejectReason ::= CHOICE {
  notRegistered              NULL,
  invalidPermission          NULL, -- exclusion by administrator or feature
  requestDenied              NULL,
  undefinedReason            NULL,
  ...,
  securityDenial             NULL,
  aliasesInconsistent        NULL, -- multiple aliases in request

  -- identify distinct people
  routeCalltoSCN             SEQUENCE OF PartyNumber,
  resourceUnavailable        NULL,
  genericDataReason          NULL,
  neededFeatureNotSupported  NULL,
  hopCountExceeded           NULL,
  incompleteAddress          NULL,
  securityError              SecurityErrors2,
  securityDHmismatch         NULL, -- mismatch of DH parameters
  noRouteToDestination       NULL, -- destination unreachable
  unallocatedNumber          NULL -- destination number unassigned
}

DisengageRequest ::= SEQUENCE --(DRQ)
                      {
  requestSeqNum            RequestSeqNum,
  endpointIdentifier       EndpointIdentifier,
  conferenceID             ConferenceIdentifier,
  callReferenceValue       CallReferenceValue,
  disengageReason          DisengageReason,
  nonStandardData          NonStandardParameterH225 OPTIONAL,
  ...,
  callIdentifier           CallIdentifier,
  gatekeeperIdentifier     GatekeeperIdentifier OPTIONAL,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  answeredCall             BOOLEAN,
  callLinkage              CallLinkage OPTIONAL,
  capacity                 CallCapacity OPTIONAL,
  circuitInfo              CircuitInfo OPTIONAL,
  usageInformation         RasUsageInformation OPTIONAL,
  terminationCause         CallTerminationCause OPTIONAL,
  serviceControl           SEQUENCE OF ServiceControlSession OPTIONAL,
  genericData              SEQUENCE OF GenericData OPTIONAL
}

DisengageReason ::= CHOICE {
  forcedDrop       NULL, -- gatekeeper is forcing the drop
  normalDrop       NULL, -- associated with normal drop
  undefinedReason  NULL,
  ...
}

DisengageConfirm ::= SEQUENCE --(DCF)
                      {
  requestSeqNum           RequestSeqNum,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  ...,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  capacity                CallCapacity OPTIONAL,
  circuitInfo             CircuitInfo OPTIONAL,
  usageInformation        RasUsageInformation OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL,
  assignedGatekeeper      AlternateGK OPTIONAL
}

DisengageReject ::= SEQUENCE --(DRJ)
                     {
  requestSeqNum           RequestSeqNum,
  rejectReason            DisengageRejectReason,
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  ...,
  altGKInfo               AltGKInfo OPTIONAL,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL
}

DisengageRejectReason ::= CHOICE {
  notRegistered       NULL, -- not registered with gatekeeper
  requestToDropOther  NULL, -- cannot request drop for others
  ...,
  securityDenial      NULL,
  securityError       SecurityErrors2
}

InfoRequest ::= SEQUENCE --(IRQ)
                 {
  requestSeqNum                  RequestSeqNum,
  callReferenceValue             CallReferenceValue,
  nonStandardData                NonStandardParameterH225 OPTIONAL,
  replyAddress                   TransportAddress OPTIONAL,
  ...,
  callIdentifier                 CallIdentifier,
  tokens                         SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                   SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue            ICV OPTIONAL,
  uuiesRequested                 UUIEsRequested OPTIONAL,
  callLinkage                    CallLinkage OPTIONAL,
  usageInfoRequested             RasUsageInfoTypes OPTIONAL,
  segmentedResponseSupported     NULL OPTIONAL,
  nextSegmentRequested           INTEGER(0..65535) OPTIONAL,
  capacityInfoRequested          NULL OPTIONAL,
  genericData                    SEQUENCE OF GenericData OPTIONAL,
  assignedGatekeeper             AlternateGK OPTIONAL
}

InfoRequestResponse ::= SEQUENCE --(IRR)
                         {
  nonStandardData         NonStandardParameterH225 OPTIONAL,
  requestSeqNum           RequestSeqNum,
  endpointType            EndpointType,
  endpointIdentifier      EndpointIdentifier,
  rasAddress              TransportAddress,
  callSignalAddress       SEQUENCE OF TransportAddress,
  endpointAlias           SEQUENCE OF AliasAddress OPTIONAL,
  perCallInfo
    SEQUENCE OF
      SEQUENCE {nonStandardData       NonStandardParameterH225 OPTIONAL,
                callReferenceValue    CallReferenceValue,
                conferenceID          ConferenceIdentifier,
                originator            BOOLEAN OPTIONAL,
                audio                 SEQUENCE OF RTPSession OPTIONAL,
                video                 SEQUENCE OF RTPSession OPTIONAL,
                data                  SEQUENCE OF TransportChannelInfo OPTIONAL,
                h245                  TransportChannelInfo,
                callSignalling        TransportChannelInfo,
                callType              CallType,
                bandWidth             BandWidth,
                callModel             CallModel,
                ...,
                callIdentifier        CallIdentifier,
                tokens                SEQUENCE OF ClearToken OPTIONAL,
                cryptoTokens          SEQUENCE OF CryptoH323Token OPTIONAL,
                substituteConfIDs     SEQUENCE OF ConferenceIdentifier,
                pdu                   SEQUENCE OF H323PDUInfo OPTIONAL,
                callLinkage           CallLinkage OPTIONAL,
                usageInformation      RasUsageInformation OPTIONAL,
                circuitInfo           CircuitInfo OPTIONAL} OPTIONAL,
  ...,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  needResponse            BOOLEAN,
  capacity                CallCapacity OPTIONAL,
  irrStatus               InfoRequestResponseStatus OPTIONAL,
  unsolicited             BOOLEAN,
  genericData             SEQUENCE OF GenericData OPTIONAL
}

InfoRequestResponseStatus ::= CHOICE {
  complete     NULL,
  incomplete   NULL,
  segment      INTEGER(0..65535),
  invalidCall  NULL,
  ...
}

H323PDUInfo ::= SEQUENCE {
  h323pdu  H323-UU-PDU,
  sent     BOOLEAN -- TRUE is sent, FALSE is received
}

InfoRequestAck ::= SEQUENCE --(IACK)
                    {
  requestSeqNum        RequestSeqNum,
  nonStandardData      NonStandardParameterH225 OPTIONAL,
  tokens               SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens         SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue  ICV OPTIONAL,
  ...
}

InfoRequestNak ::= SEQUENCE --(INAK)
                    {
  requestSeqNum        RequestSeqNum,
  nonStandardData      NonStandardParameterH225 OPTIONAL,
  nakReason            InfoRequestNakReason,
  altGKInfo            AltGKInfo OPTIONAL,
  tokens               SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens         SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue  ICV OPTIONAL,
  ...
}

InfoRequestNakReason ::= CHOICE {
  notRegistered    NULL, -- not registered with gatekeeper
  securityDenial   NULL,
  undefinedReason  NULL,
  ...,
  securityError    SecurityErrors2
}

NonStandardMessage ::= SEQUENCE {
  requestSeqNum           RequestSeqNum,
  nonStandardData         NonStandardParameterH225,
  ...,
  tokens                  SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens            SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue     ICV OPTIONAL,
  featureSet              FeatureSet OPTIONAL,
  genericData             SEQUENCE OF GenericData OPTIONAL
}

UnknownMessageResponse ::= SEQUENCE -- (XRS)
                            {
  requestSeqNum            RequestSeqNum,
  ...,
  tokens                   SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens             SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue      ICV OPTIONAL,
  messageNotUnderstood     OCTET STRING
}

RequestInProgress ::= SEQUENCE -- (RIP)
                       {
  requestSeqNum        RequestSeqNum,
  nonStandardData      NonStandardParameterH225 OPTIONAL,
  tokens               SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens         SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue  ICV OPTIONAL,
  delay                INTEGER(1..65535),
  ...
}

ResourcesAvailableIndicate ::= SEQUENCE --(RAI)
                                {
  requestSeqNum         RequestSeqNum,
  protocolIdentifier    ProtocolIdentifier,
  nonStandardData       NonStandardParameterH225 OPTIONAL,
  endpointIdentifier    EndpointIdentifier,
  protocols             SEQUENCE OF SupportedProtocols,
  almostOutOfResources  BOOLEAN,
  tokens                SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens          SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue   ICV OPTIONAL,
  ...,
  capacity              CallCapacity OPTIONAL,
  genericData           SEQUENCE OF GenericData OPTIONAL
}

ResourcesAvailableConfirm ::= SEQUENCE --(RAC)
                               {
  requestSeqNum        RequestSeqNum,
  protocolIdentifier   ProtocolIdentifier,
  nonStandardData      NonStandardParameterH225 OPTIONAL,
  tokens               SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens         SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue  ICV OPTIONAL,
  ...,
  genericData          SEQUENCE OF GenericData OPTIONAL
}

ServiceControlIndication ::= SEQUENCE --(SCI)
                              {
  requestSeqNum        RequestSeqNum,
  nonStandardData      NonStandardParameterH225 OPTIONAL,
  serviceControl       SEQUENCE OF ServiceControlSession,
  endpointIdentifier   EndpointIdentifier OPTIONAL,
  callSpecific
    SEQUENCE {callIdentifier  CallIdentifier,
              conferenceID    ConferenceIdentifier,
              answeredCall    BOOLEAN,
              ...} OPTIONAL,
  tokens               SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens         SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue  ICV OPTIONAL,
  featureSet           FeatureSet OPTIONAL,
  genericData          SEQUENCE OF GenericData OPTIONAL,
  ...
}

ServiceControlResponse ::= SEQUENCE --(SCR)
                            {
  requestSeqNum        RequestSeqNum,
  result
    CHOICE {started                    NULL,
            failed                     NULL,
            stopped                    NULL,
            notAvailable               NULL,
            neededFeatureNotSupported  NULL,
            ...} OPTIONAL,
  nonStandardData      NonStandardParameterH225 OPTIONAL,
  tokens               SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens         SEQUENCE OF CryptoH323Token OPTIONAL,
  integrityCheckValue  ICV OPTIONAL,
  featureSet           FeatureSet OPTIONAL,
  genericData          SEQUENCE OF GenericData OPTIONAL,
  ...
}

END -- of ASN.1

-- Generated by Asnp, the ASN.1 pretty-printer of France Telecom R&D

-- H235-SECURITY-MESSAGES.asn
--
-- Taken from ITU ASN.1 database
-- http://www.itu.int/ITU-T/asn1/database/itu-t/h/h235.0/2005/H235-SECURITY-MESSAGES.asn
--

-- Module H235-SECURITY-MESSAGES (H.235.0:09/2005)
H235-SECURITY-MESSAGES DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

-- EXPORTS All
ChallengeString ::= OCTET STRING(SIZE (8..128))

TimeStamp ::= INTEGER(1..4294967295) -- seconds since 00:00


-- 1/1/1970 UTC
RandomVal ::= INTEGER -- 32-bit Integer

Password ::= BMPString(SIZE (1..128))

Identifier ::= BMPString(SIZE (1..128))

KeyMaterial ::= BIT STRING(SIZE (1..2048))

NonStandardParameterH235 ::= SEQUENCE {
  nonStandardIdentifier  OBJECT IDENTIFIER,
  data                   OCTET STRING
}

-- if local octet representations of these bit strings are used they shall
-- utilize standard Network Octet ordering (e.g., Big Endian)
DHset ::= SEQUENCE {
  halfkey    BIT STRING(SIZE (0..2048)), -- = g^x mod n
  modSize    BIT STRING(SIZE (0..2048)), --  n
  generator  BIT STRING(SIZE (0..2048)), -- g
  ...
}

ECpoint ::=
  SEQUENCE -- uncompressed (x, y) affine coordinate representation of

   -- an elliptic curve point
  {
  x  BIT STRING(SIZE (0..511)) OPTIONAL,
  y  BIT STRING(SIZE (0..511)) OPTIONAL,
  ...
}

ECKASDH ::=
  CHOICE -- parameters for elliptic curve key agreement scheme Diffie-Hellman
   {
  eckasdhp
    SEQUENCE-- parameters for elliptic curves of prime field-- {public-key
                                                                  ECpoint, -- This field contains representation of --
                                                                -- the ECKAS-DHp public key value. This field contains the
                                                                -- initiator's ECKAS-DHp public key value (aP) when this
                                                                -- information element is sent from originator to receiver. This
                                                                -- field contains the responder's ECKAS-DHp public key value (bP)
                                                                -- when this information element is sent back from receiver to
                                                                -- originator.
                                                                modulus
                                                                  BIT STRING
                                                                    (SIZE (0..
                                                                    511)), -- This field contains --
                                                                -- representation of the ECKAS-DHp public modulus value (p).
                                                                base
                                                                  ECpoint, -- This field contains representation of the --
                                                                -- ECKAS-DHp public base (P).
                                                                weierstrassA
                                                                  BIT STRING
                                                                    (SIZE (0..
                                                                    511)), -- This field contains --
                                                                -- representation of the ECKAS-DHp Weierstrass coefficient (a).
                                                                weierstrassB
                                                                  BIT STRING
                                                                    (SIZE (0..
                                                                    511))-- This field contains --
                                                                -- representation of the ECKAS-DHp Weierstrass coefficient (b).
  },
  eckasdh2
    SEQUENCE-- parameters for elliptic curves of characteristic 2 -- {public-key

                                                                    ECpoint, -- This field contains representation of --
                                                                    -- the ECKAS-DH2 public key value.
                                                                    -- This field contains the initiator's ECKAS-DH2 public key value
                                                                    -- (aP) when this information element is sent from originator to
                                                                    -- receiver. This field contains the responder's ECKAS-DH2 public
                                                                    -- key value (bP) when this information element is sent back from
                                                                    -- receiver to originator.
                                                                    fieldSize

                                                                    BIT STRING
                                                                    (SIZE (0..
                                                                    511)), -- This field contains --
                                                                    -- representation of the ECKAS-DH2 field size value (m).
                                                                    base

                                                                    ECpoint, -- This field contains representation of the --
                                                                    -- ECKAS-DH2 public base (P).
                                                                    weierstrassA

                                                                    BIT STRING
                                                                    (SIZE (0..
                                                                    511)), -- This field contains --
                                                                    -- representation of the ECKAS-DH2 Weierstrass coefficient (a).
                                                                    weierstrassB

                                                                    BIT STRING
                                                                    (SIZE (0..
                                                                    511))-- This field contains --
                                                                    -- representation of the ECKAS-DH2 Weierstrass coefficient (b).
  },
  ...
}

ECGDSASignature ::=
  SEQUENCE -- parameters for elliptic curve digital signature

   -- algorithm
  {
  r  BIT STRING(SIZE (0..511)), -- This field contains the

  -- representation of the r component of the ECGDSA digital
  -- signature.
  s  BIT STRING(SIZE (0..511))-- This field contains the --
  -- representation of the s component of the ECGDSA digital
  -- signature.
}

TypedCertificate ::= SEQUENCE {
  type         OBJECT IDENTIFIER,
  certificate  OCTET STRING,
  ...
}

AuthenticationBES ::= CHOICE {
  default  NULL, -- encrypted ClearToken
  radius   NULL, -- RADIUS-challenge/response
  ...
}

AuthenticationMechanism ::= CHOICE {
  dhExch             NULL, -- Diffie-Hellman
  pwdSymEnc          NULL, -- password with symmetric encryption
  pwdHash            NULL, -- password with hashing
  certSign           NULL, -- Certificate with signature
  ipsec              NULL, -- IPSEC based connection
  tls                NULL,
  nonStandard        NonStandardParameterH235, -- something else.
  ...,
  authenticationBES  AuthenticationBES, -- user authentication for BES
  keyExch            OBJECT IDENTIFIER -- key exchange profile
}

ClearToken ::= SEQUENCE -- a "token" may contain multiple value types.
                {
  tokenOID        OBJECT IDENTIFIER,
  timeStamp       TimeStamp OPTIONAL,
  password        Password OPTIONAL,
  dhkey           DHset OPTIONAL,
  challenge       ChallengeString OPTIONAL,
  random          RandomVal OPTIONAL,
  certificate     TypedCertificate OPTIONAL,
  generalID       Identifier OPTIONAL,
  nonStandard     NonStandardParameterH235 OPTIONAL,
  ...,
  eckasdhkey      ECKASDH OPTIONAL, -- elliptic curve Key Agreement

  -- Scheme-Diffie Hellman Analogue
  -- (ECKAS-DH)
  sendersID       Identifier OPTIONAL,
  h235Key         H235Key OPTIONAL, -- central distributed key in V3
  profileInfo     SEQUENCE OF ProfileElement OPTIONAL -- profile-specific
}

--	An object identifier should be placed in the tokenOID field when a
--	ClearToken is included directly in a message (as opposed to being
--	encrypted). In all other cases, an application should use the
--	object identifier { 0 0 } to indicate that the tokenOID value is not
--	present.
--	Start all the cryptographic parameterized types here...
--
ProfileElement ::= SEQUENCE {
  elementID  INTEGER(0..255), -- element identifier, as defined by

  -- profile
  paramS     Params OPTIONAL, -- any element-specific parameters
  element    Element OPTIONAL, -- value in required form
  ...
}

Element ::= CHOICE {
  octets   OCTET STRING,
  integer  INTEGER,
  bits     BIT STRING,
  name     BMPString,
  flag     BOOLEAN,
  ...
}

SIGNED{ToBeSigned} ::= SEQUENCE {
  toBeSigned    ToBeSigned,
  algorithmOID  OBJECT IDENTIFIER,
  paramS        Params, -- any "runtime" parameters
  signature     BIT STRING -- could be an RSA or an ASN.1 coded ECGDSA Signature
}(CONSTRAINED BY { -- Verify or Sign Certificate --})

ENCRYPTED{ToBeEncrypted} ::= SEQUENCE {
  algorithmOID   OBJECT IDENTIFIER,
  paramS         Params, -- any "runtime" parameters
  encryptedData  OCTET STRING
}(CONSTRAINED BY { -- Encrypt or Decrypt --ToBeEncrypted})

HASHED{ToBeHashed} ::= SEQUENCE {
  algorithmOID  OBJECT IDENTIFIER,
  paramS        Params, -- any "runtime" parameters
  hash          BIT STRING
}(CONSTRAINED BY { -- Hash --ToBeHashed})

IV8 ::= OCTET STRING(SIZE (8)) -- initial value for 64-bit block ciphers


IV16 ::= OCTET STRING(SIZE (16)) -- initial value for 128-bit block ciphers


-- signing algorithm used must select one of these types of parameters
-- needed by receiving end of signature.
Params ::= SEQUENCE {
  ranInt        INTEGER OPTIONAL, -- some integer value
  iv8           IV8 OPTIONAL, -- 8-octet initialization vector
  ...,
  iv16          IV16 OPTIONAL, -- 16-octet initialization vector
  iv            OCTET STRING OPTIONAL, -- arbitrary length initialization vector
  clearSalt     OCTET STRING OPTIONAL -- unencrypted salting key for encryption
}

EncodedGeneralToken ::=
  TYPE-IDENTIFIER.&Type(ClearToken -- general usage token --)

PwdCertToken ::=
  ClearToken(WITH COMPONENTS {
               ...,
               timeStamp  PRESENT,
               generalID  PRESENT
             })

EncodedPwdCertToken ::= TYPE-IDENTIFIER.&Type(PwdCertToken)

CryptoToken ::= CHOICE {
  cryptoEncryptedToken
    SEQUENCE-- General purpose/application specific token-- {tokenOID
                                                               OBJECT
                                                                 IDENTIFIER,
                                                             token
                                                               ENCRYPTED
                                                                 {EncodedGeneralToken}
  },
  cryptoSignedToken
    SEQUENCE-- General purpose/application specific token-- {tokenOID
                                                               OBJECT
                                                                 IDENTIFIER,
                                                             token
                                                               SIGNED
                                                                 {EncodedGeneralToken}
  },
  cryptoHashedToken
    SEQUENCE-- General purpose/application specific token-- {tokenOID
                                                               OBJECT
                                                                 IDENTIFIER,
                                                             hashedVals
                                                               ClearToken,
                                                             token
                                                               HASHED
                                                                 {EncodedGeneralToken}
  },
  cryptoPwdEncr         ENCRYPTED{EncodedPwdCertToken},
  ...
}

-- These allow the passing of session keys within the H.245 OLC structure.
-- They are encoded as standalone ASN.1 and based as an OCTET STRING within
-- H.245
H235Key ::=
  CHOICE -- This is used with the H.245 or ClearToken "h235Key" field
   {
  secureChannel       KeyMaterial,
  sharedSecret        ENCRYPTED{EncodedKeySyncMaterial},
  certProtectedKey    SIGNED{EncodedKeySignedMaterial},
  ...,
  secureSharedSecret  V3KeySyncMaterial -- for H.235 V3 endpoints
}

KeySignedMaterial ::= SEQUENCE {
  generalId  Identifier, -- slave's alias
  mrandom    RandomVal, -- master's random value
  srandom    RandomVal OPTIONAL, -- slave's random value
  timeStamp  TimeStamp OPTIONAL, -- master's timestamp for unsolicited EU
  encrptval  ENCRYPTED{EncodedKeySyncMaterial}
}

EncodedKeySignedMaterial ::= TYPE-IDENTIFIER.&Type(KeySignedMaterial)

H235CertificateSignature ::= SEQUENCE {
  certificate      TypedCertificate,
  responseRandom   RandomVal,
  requesterRandom  RandomVal OPTIONAL,
  signature        SIGNED{EncodedReturnSig},
  ...
}

ReturnSig ::= SEQUENCE {
  generalId       Identifier, -- slave's alias
  responseRandom  RandomVal,
  requestRandom   RandomVal OPTIONAL,
  certificate     TypedCertificate OPTIONAL -- requested certificate
}

EncodedReturnSig ::= TYPE-IDENTIFIER.&Type(ReturnSig)

KeySyncMaterial ::= SEQUENCE {
  generalID    Identifier,
  keyMaterial  KeyMaterial,
  ...
}

EncodedKeySyncMaterial ::= TYPE-IDENTIFIER.&Type(KeySyncMaterial)

V3KeySyncMaterial ::= SEQUENCE {
  generalID              Identifier OPTIONAL, -- peer terminal ID
  algorithmOID           OBJECT IDENTIFIER OPTIONAL, -- encryption algorithm
  paramS                 Params, -- IV
  encryptedSessionKey    OCTET STRING OPTIONAL, -- encrypted session key
  encryptedSaltingKey    OCTET STRING OPTIONAL, -- encrypted media salting

  -- key
  clearSaltingKey        OCTET STRING OPTIONAL, -- unencrypted media salting

  -- key
  paramSsalt             Params OPTIONAL, -- IV (and clear salt) for salting

  -- key encryption
  keyDerivationOID       OBJECT IDENTIFIER OPTIONAL, -- key derivation

  -- method
  ...,
  genericKeyMaterial     OCTET STRING OPTIONAL -- ASN.1-encoded key material--
  -- form is dependent on associated media encryption tag
}

END -- End of H235-SECURITY-MESSAGES DEFINITIONS

-- Generated by Asnp, the ASN.1 pretty-printer of France Telecom R&D

-- MULTIMEDIA-SYSTEM-CONTROL.asn
--
-- Taken from ITU ASN.1 database
-- http://www.itu.int/ITU-T/formal-language/itu-t/h/h245/2009v15/MULTIMEDIA-SYSTEM-CONTROL.asn
--

-- Module MULTIMEDIA-SYSTEM-CONTROL (H.245:12/2009)
MULTIMEDIA-SYSTEM-CONTROL {itu-t(0) recommendation(0) h(8) h245(245)
  version(0) 15 multimedia-system-control(0)} DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

-- Export all symbols
-- =======================================================================
-- Top level Messages
-- =======================================================================
MultimediaSystemControlMessage ::= CHOICE {
  request     RequestMessage,
  response    ResponseMessage,
  command     CommandMessage,
  indication  IndicationMessage,
  ...
}

-- A RequestMessage results in action and requires an immediate response
RequestMessage ::= CHOICE {
  nonStandard                NonStandardMessageH245,
  masterSlaveDetermination   MasterSlaveDetermination,
  terminalCapabilitySet      TerminalCapabilitySet,
  openLogicalChannel         OpenLogicalChannel,
  closeLogicalChannel        CloseLogicalChannel,
  requestChannelClose        RequestChannelClose,
  multiplexEntrySend         MultiplexEntrySend,
  requestMultiplexEntry      RequestMultiplexEntry,
  requestMode                RequestMode,
  roundTripDelayRequest      RoundTripDelayRequest,
  maintenanceLoopRequest     MaintenanceLoopRequest,
  ...,
  communicationModeRequest   CommunicationModeRequest,
  conferenceRequest          ConferenceRequest,
  multilinkRequest           MultilinkRequest,
  logicalChannelRateRequest  LogicalChannelRateRequest,
  genericRequest             GenericMessage
}

-- A ResponseMessage is the response to a RequestMessage
ResponseMessage ::= CHOICE {
  nonStandard                     NonStandardMessageH245,
  masterSlaveDeterminationAck     MasterSlaveDeterminationAck,
  masterSlaveDeterminationReject  MasterSlaveDeterminationReject,
  terminalCapabilitySetAck        TerminalCapabilitySetAck,
  terminalCapabilitySetReject     TerminalCapabilitySetReject,
  openLogicalChannelAck           OpenLogicalChannelAck,
  openLogicalChannelReject        OpenLogicalChannelReject,
  closeLogicalChannelAck          CloseLogicalChannelAck,
  requestChannelCloseAck          RequestChannelCloseAck,
  requestChannelCloseReject       RequestChannelCloseReject,
  multiplexEntrySendAck           MultiplexEntrySendAck,
  multiplexEntrySendReject        MultiplexEntrySendReject,
  requestMultiplexEntryAck        RequestMultiplexEntryAck,
  requestMultiplexEntryReject     RequestMultiplexEntryReject,
  requestModeAck                  RequestModeAck,
  requestModeReject               RequestModeReject,
  roundTripDelayResponse          RoundTripDelayResponse,
  maintenanceLoopAck              MaintenanceLoopAck,
  maintenanceLoopReject           MaintenanceLoopReject,
  ...,
  communicationModeResponse       CommunicationModeResponse,
  conferenceResponse              ConferenceResponse,
  multilinkResponse               MultilinkResponse,
  logicalChannelRateAcknowledge   LogicalChannelRateAcknowledge,
  logicalChannelRateReject        LogicalChannelRateReject,
  genericResponse                 GenericMessage
}

-- A CommandMessage requires action, but no explicit response
CommandMessage ::= CHOICE {
  nonStandard                            NonStandardMessageH245,
  maintenanceLoopOffCommand              MaintenanceLoopOffCommand,
  sendTerminalCapabilitySet              SendTerminalCapabilitySet,
  encryptionCommand                      EncryptionCommand,
  flowControlCommand                     FlowControlCommand,
  endSessionCommand                      EndSessionCommand,
  miscellaneousCommand                   MiscellaneousCommand,
  ...,
  communicationModeCommand               CommunicationModeCommand,
  conferenceCommand                      ConferenceCommand,
  h223MultiplexReconfiguration           H223MultiplexReconfiguration,
  newATMVCCommand                        NewATMVCCommand,
  mobileMultilinkReconfigurationCommand  MobileMultilinkReconfigurationCommand,
  genericCommand                         GenericMessage
}

-- An IndicationMessage is information that does not require action or response
IndicationMessage ::= CHOICE {
  nonStandard                               NonStandardMessageH245,
  functionNotUnderstood                     FunctionNotUnderstood,
  masterSlaveDeterminationRelease           MasterSlaveDeterminationRelease,
  terminalCapabilitySetRelease              TerminalCapabilitySetRelease,
  openLogicalChannelConfirm                 OpenLogicalChannelConfirm,
  requestChannelCloseRelease                RequestChannelCloseRelease,
  multiplexEntrySendRelease                 MultiplexEntrySendRelease,
  requestMultiplexEntryRelease              RequestMultiplexEntryRelease,
  requestModeRelease                        RequestModeRelease,
  miscellaneousIndication                   MiscellaneousIndication,
  jitterIndication                          JitterIndication,
  h223SkewIndication                        H223SkewIndication,
  newATMVCIndication                        NewATMVCIndication,
  userInput                                 UserInputIndication,
  ...,
  h2250MaximumSkewIndication                H2250MaximumSkewIndication,
  mcLocationIndication                      MCLocationIndication,
  conferenceIndication                      ConferenceIndication,
  vendorIdentification                      VendorIdentification,
  functionNotSupported                      FunctionNotSupported,
  multilinkIndication                       MultilinkIndication,
  logicalChannelRateRelease                 LogicalChannelRateRelease,
  flowControlIndication                     FlowControlIndication,
  mobileMultilinkReconfigurationIndication
    MobileMultilinkReconfigurationIndication,
  genericIndication                         GenericMessage
}

-- SequenceNumber is defined here as it is used in a number of Messages
SequenceNumber ::= INTEGER(0..255)

-- =============================================================================
-- Generic Message definitions
-- =============================================================================
GenericMessage ::= SEQUENCE {
  messageIdentifier     CapabilityIdentifier,
  subMessageIdentifier  INTEGER(0..127) OPTIONAL,
  messageContent        SEQUENCE OF GenericParameter OPTIONAL,
  ...
}

GenericInformation ::= GenericMessage

-- =============================================================================
-- Non-standard Message definitions
-- =============================================================================
NonStandardMessageH245 ::= SEQUENCE {nonStandardData  NonStandardParameter,
                                 ...
}

NonStandardParameter ::= SEQUENCE {
  nonStandardIdentifier  NonStandardIdentifier,
  data                   OCTET STRING
}

NonStandardIdentifier ::= CHOICE {
  object           OBJECT IDENTIFIER,
  h221NonStandard
    SEQUENCE {t35CountryCode    INTEGER(0..255), -- country, per --
              -- Annex A/T.35
              t35Extension      INTEGER(0..255),
              -- assigned nationally unless
              -- t35CountryCode is binary
              -- 1111 1111, in which case it shall
              -- contain the country code
              -- according to Annex B/T.35
              manufacturerCode  INTEGER(0..65535) -- assigned nationally
  }
}

-- =============================================================================
-- Master-slave determination definitions
-- =============================================================================
MasterSlaveDetermination ::= SEQUENCE {
  terminalType               INTEGER(0..255),
  statusDeterminationNumber  INTEGER(0..16777215),
  ...
}

MasterSlaveDeterminationAck ::= SEQUENCE {
  decision  CHOICE {master  NULL,
                    slave   NULL},
  ...
}

MasterSlaveDeterminationReject ::= SEQUENCE {
  cause  CHOICE {identicalNumbers  NULL,
                 ...},
  ...
}

MasterSlaveDeterminationRelease ::= SEQUENCE {...
}

-- =============================================================================
-- Capability exchange definitions
-- =============================================================================
TerminalCapabilitySet ::= SEQUENCE {
  sequenceNumber         SequenceNumber,
  protocolIdentifier     OBJECT IDENTIFIER,
  -- shall be set to the value
  -- {itu-t (0) recommendation (0) h (8) 245
  -- version (0) 15}
  multiplexCapability    MultiplexCapability OPTIONAL,
  capabilityTable        SET SIZE (1..256) OF CapabilityTableEntry OPTIONAL,
  capabilityDescriptors  SET SIZE (1..256) OF CapabilityDescriptor OPTIONAL,
  ...,
  genericInformation     SEQUENCE OF GenericInformation OPTIONAL
  -- generic information associated
  -- with the message
}

CapabilityTableEntry ::= SEQUENCE {
  capabilityTableEntryNumber  CapabilityTableEntryNumber,
  capability                  Capability OPTIONAL
}

CapabilityDescriptor ::= SEQUENCE {
  capabilityDescriptorNumber  CapabilityDescriptorNumber,
  simultaneousCapabilities
    SET SIZE (1..256) OF AlternativeCapabilitySet OPTIONAL
}

AlternativeCapabilitySet ::=
  SEQUENCE SIZE (1..256) OF CapabilityTableEntryNumber

CapabilityTableEntryNumber ::= INTEGER(1..65535)

CapabilityDescriptorNumber ::= INTEGER(0..255)

TerminalCapabilitySetAck ::= SEQUENCE {
  sequenceNumber         SequenceNumber,
  ...,
  genericInformation     SEQUENCE OF GenericInformation OPTIONAL
  -- generic information associated
  -- with the message
}

TerminalCapabilitySetReject ::= SEQUENCE {
  sequenceNumber         SequenceNumber,
  cause
    CHOICE {unspecified                 NULL,
            undefinedTableEntryUsed     NULL,
            descriptorCapacityExceeded  NULL,
            tableEntryCapacityExceeded
              CHOICE {highestEntryNumberProcessed  CapabilityTableEntryNumber,
                      noneProcessed                NULL},
            ...},
  ...,
  genericInformation     SEQUENCE OF GenericInformation OPTIONAL
  -- generic information associated
  -- with the message
}

TerminalCapabilitySetRelease ::= SEQUENCE {
  ...,
  genericInformation     SEQUENCE OF GenericInformation OPTIONAL
  -- generic information associated
  -- with the message
}

-- =============================================================================
-- Capability exchange definitions: top level capability description
-- =============================================================================
Capability ::= CHOICE {
  nonStandard                                    NonStandardParameter,
  receiveVideoCapability                         VideoCapability,
  transmitVideoCapability                        VideoCapability,
  receiveAndTransmitVideoCapability              VideoCapability,
  receiveAudioCapability                         AudioCapability,
  transmitAudioCapability                        AudioCapability,
  receiveAndTransmitAudioCapability              AudioCapability,
  receiveDataApplicationCapability               DataApplicationCapability,
  transmitDataApplicationCapability              DataApplicationCapability,
  receiveAndTransmitDataApplicationCapability    DataApplicationCapability,
  h233EncryptionTransmitCapability               BOOLEAN,
  h233EncryptionReceiveCapability
    SEQUENCE {h233IVResponseTime  INTEGER(0..255), -- units milliseconds --
              ...},
  ...,
  conferenceCapability                           ConferenceCapability,
  h235SecurityCapability                         H235SecurityCapability,
  maxPendingReplacementFor                       INTEGER(0..255),
  receiveUserInputCapability                     UserInputCapability,
  transmitUserInputCapability                    UserInputCapability,
  receiveAndTransmitUserInputCapability          UserInputCapability,
  genericControlCapability                       GenericCapability,
  receiveMultiplexedStreamCapability             MultiplexedStreamCapability,
  transmitMultiplexedStreamCapability            MultiplexedStreamCapability,
  receiveAndTransmitMultiplexedStreamCapability  MultiplexedStreamCapability,
  receiveRTPAudioTelephonyEventCapability        AudioTelephonyEventCapability,
  receiveRTPAudioToneCapability                  AudioToneCapability,
  depFecCapability                               DepFECCapability, -- Deprecated, do not use
  multiplePayloadStreamCapability
    MultiplePayloadStreamCapability,
  fecCapability                                  FECCapability,
  redundancyEncodingCap                          RedundancyEncodingCapability,
  oneOfCapabilities                              AlternativeCapabilitySet
}

H235SecurityCapability ::= SEQUENCE {
  encryptionAuthenticationAndIntegrity  EncryptionAuthenticationAndIntegrity,
  mediaCapability                       CapabilityTableEntryNumber,
  -- NOTE - The mediaCapability shall refer to Capability Table Entries
  -- that do contain, directly or indirectly, one or more transmit,
  -- receive, or receiveAndTransmit AudioCapability, VideoCapability,
  -- DataApplicationCapability, or similar capabilities indicated by a
  -- NonStandardParameter or GenericCapability only
  ...
}

-- =============================================================================
-- Capability exchange definitions: Multiplex capabilities
-- =============================================================================
MultiplexCapability ::= CHOICE {
  nonStandard                 NonStandardParameter,
  h222Capability              H222Capability,
  h223Capability              H223Capability,
  v76Capability               V76Capability,
  ...,
  h2250Capability             H2250Capability,
  genericMultiplexCapability  GenericCapability
}

H222Capability ::= SEQUENCE {
  numberOfVCs   INTEGER(1..256),
  vcCapability  SET OF VCCapability,
  ...
}

VCCapability ::= SEQUENCE {
  aal1
    SEQUENCE {nullClockRecovery       BOOLEAN,
              srtsClockRecovery       BOOLEAN,
              adaptiveClockRecovery   BOOLEAN,
              nullErrorCorrection     BOOLEAN,
              longInterleaver         BOOLEAN,
              shortInterleaver        BOOLEAN,
              errorCorrectionOnly     BOOLEAN,
              structuredDataTransfer  BOOLEAN,
              partiallyFilledCells    BOOLEAN,
              ...} OPTIONAL,
  aal5
    SEQUENCE {forwardMaximumSDUSize   INTEGER(0..65535), -- units octets--
              backwardMaximumSDUSize  INTEGER(0..65535), -- units octets--
              ...} OPTIONAL,
  transportStream    BOOLEAN,
  programStream      BOOLEAN,
  availableBitRates
    SEQUENCE {type
                CHOICE {singleBitRate    INTEGER(1..65535), -- units 64 kbit/s--
                        rangeOfBitRates
                          SEQUENCE {lowerBitRate   INTEGER(1..65535), -- units 64 kbit/s--
                                    higherBitRate  INTEGER(1..65535) -- units 64 kbit/s
                        }},
              ...},
  ...,
  aal1ViaGateway
    SEQUENCE {gatewayAddress          SET SIZE (1..256) OF Q2931Address,
              nullClockRecovery       BOOLEAN,
              srtsClockRecovery       BOOLEAN,
              adaptiveClockRecovery   BOOLEAN,
              nullErrorCorrection     BOOLEAN,
              longInterleaver         BOOLEAN,
              shortInterleaver        BOOLEAN,
              errorCorrectionOnly     BOOLEAN,
              structuredDataTransfer  BOOLEAN,
              partiallyFilledCells    BOOLEAN,
              ...} OPTIONAL
}

H223Capability ::= SEQUENCE {
  transportWithI-frames                 BOOLEAN, -- I-frame transport

  -- of H.245
  videoWithAL1                          BOOLEAN,
  videoWithAL2                          BOOLEAN,
  videoWithAL3                          BOOLEAN,
  audioWithAL1                          BOOLEAN,
  audioWithAL2                          BOOLEAN,
  audioWithAL3                          BOOLEAN,
  dataWithAL1                           BOOLEAN,
  dataWithAL2                           BOOLEAN,
  dataWithAL3                           BOOLEAN,
  maximumAl2SDUSize                     INTEGER(0..65535), -- units octets
  maximumAl3SDUSize                     INTEGER(0..65535), -- units octets
  maximumDelayJitter                    INTEGER(0..1023), -- units milliseconds
  h223MultiplexTableCapability
    CHOICE {basic     NULL,
            enhanced
              SEQUENCE {maximumNestingDepth        INTEGER(1..15),
                        maximumElementListSize     INTEGER(2..255),
                        maximumSubElementListSize  INTEGER(2..255),
                        ...}},
  ...,
  maxMUXPDUSizeCapability               BOOLEAN,
  nsrpSupport                           BOOLEAN,
  mobileOperationTransmitCapability
    SEQUENCE {modeChangeCapability  BOOLEAN,
              h223AnnexA            BOOLEAN,
              h223AnnexADoubleFlag  BOOLEAN,
              h223AnnexB            BOOLEAN,
              h223AnnexBwithHeader  BOOLEAN,
              ...} OPTIONAL,
  h223AnnexCCapability                  H223AnnexCCapability OPTIONAL,
  bitRate                               INTEGER(1..19200) OPTIONAL, -- units of

  -- 100 bit/s
  mobileMultilinkFrameCapability
    SEQUENCE {maximumSampleSize     INTEGER(1..255), -- units octets--
              maximumPayloadLength  INTEGER(1..65025), -- units octets--
              ...} OPTIONAL
}

H223AnnexCCapability ::= SEQUENCE {
  videoWithAL1M        BOOLEAN,
  videoWithAL2M        BOOLEAN,
  videoWithAL3M        BOOLEAN,
  audioWithAL1M        BOOLEAN,
  audioWithAL2M        BOOLEAN,
  audioWithAL3M        BOOLEAN,
  dataWithAL1M         BOOLEAN,
  dataWithAL2M         BOOLEAN,
  dataWithAL3M         BOOLEAN,
  alpduInterleaving    BOOLEAN,
  maximumAL1MPDUSize   INTEGER(0..65535), -- units octets
  maximumAL2MSDUSize   INTEGER(0..65535), -- units octets
  maximumAL3MSDUSize   INTEGER(0..65535), -- units octets
  ...,
  rsCodeCapability     BOOLEAN OPTIONAL
}

V76Capability ::= SEQUENCE {
  suspendResumeCapabilitywAddress   BOOLEAN,
  suspendResumeCapabilitywoAddress  BOOLEAN,
  rejCapability                     BOOLEAN,
  sREJCapability                    BOOLEAN,
  mREJCapability                    BOOLEAN,
  crc8bitCapability                 BOOLEAN,
  crc16bitCapability                BOOLEAN,
  crc32bitCapability                BOOLEAN,
  uihCapability                     BOOLEAN,
  numOfDLCS                         INTEGER(2..8191),
  twoOctetAddressFieldCapability    BOOLEAN,
  loopBackTestCapability            BOOLEAN,
  n401Capability                    INTEGER(1..4095),
  maxWindowSizeCapability           INTEGER(1..127),
  v75Capability                     V75Capability,
  ...
}

V75Capability ::= SEQUENCE {audioHeader  BOOLEAN,
                            ...
}

H2250Capability ::= SEQUENCE {
  maximumAudioDelayJitter                 INTEGER(0..1023), -- units in

  -- milliseconds
  receiveMultipointCapability             MultipointCapability,
  transmitMultipointCapability            MultipointCapability,
  receiveAndTransmitMultipointCapability  MultipointCapability,
  mcCapability
    SEQUENCE {centralizedConferenceMC    BOOLEAN,
              decentralizedConferenceMC  BOOLEAN,
              ...},
  rtcpVideoControlCapability              BOOLEAN, -- FIR and NACK
  mediaPacketizationCapability            MediaPacketizationCapability,
  ...,
  transportCapability                     TransportCapability OPTIONAL,
  redundancyEncodingCapability
    SEQUENCE SIZE (1..256) OF RedundancyEncodingCapability OPTIONAL,
  logicalChannelSwitchingCapability       BOOLEAN,
  t120DynamicPortCapability               BOOLEAN
}

MediaPacketizationCapability ::= SEQUENCE {
  h261aVideoPacketization  BOOLEAN,
  ...,
  rtpPayloadType           SEQUENCE SIZE (1..256) OF RTPPayloadType OPTIONAL
}

RSVPParameters ::= SEQUENCE {
  qosMode     QOSMode OPTIONAL,
  tokenRate   INTEGER(1..4294967295) OPTIONAL,
  -- rate in bytes/s
  bucketSize  INTEGER(1..4294967295) OPTIONAL,
  -- size in bytes
  peakRate    INTEGER(1..4294967295) OPTIONAL,
  -- peak bandwidth bytes/s
  minPoliced  INTEGER(1..4294967295) OPTIONAL,
  --
  maxPktSize  INTEGER(1..4294967295) OPTIONAL,
  -- size in bytes
  ...
}

QOSMode ::= CHOICE {guaranteedQOS   NULL,
                    controlledLoad  NULL,
                    ...
}

ATMParameters ::= SEQUENCE {
  maxNTUSize  INTEGER(0..65535), -- units in octets
  atmUBR      BOOLEAN, -- unspecified bit rate
  atmrtVBR    BOOLEAN, -- real time variable

  -- bit rate
  atmnrtVBR   BOOLEAN, -- non real time

  -- variable bit rate
  atmABR      BOOLEAN, -- available bit rate
  atmCBR      BOOLEAN, -- constant bit rate
  ...
}

ServicePriorityValue ::= SEQUENCE {
  nonStandardParameter  NonStandardParameter OPTIONAL,
  ...,
  value                 INTEGER(0..255)
}

ServicePriority ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  servicePrioritySignalled  BOOLEAN,
  servicePriorityValue      ServicePriorityValue OPTIONAL,
  ...,
  serviceClass              INTEGER(0..4095) OPTIONAL,
  serviceSubclass           INTEGER(0..255) OPTIONAL
}

AuthorizationParameters ::= SEQUENCE {
  nonStandardData  NonStandardParameter OPTIONAL,
  ...
}

QOSType ::= CHOICE {desired   NULL,
                    required  NULL,
                    ...
}

QOSClass ::= CHOICE {
  class0  NULL,
  class1  NULL,
  class2  NULL,
  class3  NULL,
  class4  NULL,
  class5  NULL,
  ...
}

QOSDescriptor ::= SEQUENCE {
  nonStandardData  NonStandardParameter OPTIONAL,
  qosType          QOSType,
  qosClass         QOSClass,
  ...
}

GenericTransportParameters ::= SEQUENCE {
  nonStandardData  NonStandardParameter OPTIONAL,
  averageRate      INTEGER(1..4294967295) OPTIONAL,
  -- average bandwidth bytes/s
  burst            INTEGER(1..4294967295) OPTIONAL,
  --  size in bytes
  peakRate         INTEGER(1..4294967295) OPTIONAL,
  -- peak bandwidth bytes/s
  maxPktSize       INTEGER(1..4294967295) OPTIONAL,
  -- size in bytes
  ...
}

QOSCapability ::= SEQUENCE {
  nonStandardData                NonStandardParameter OPTIONAL,
  rsvpParameters                 RSVPParameters OPTIONAL,
  atmParameters                  ATMParameters OPTIONAL,
  ...,
  localQoS                       BOOLEAN OPTIONAL,
  genericTransportParameters     GenericTransportParameters OPTIONAL,
  servicePriority                ServicePriority OPTIONAL,
  authorizationParameter         AuthorizationParameters OPTIONAL,
  qosDescriptor                  QOSDescriptor OPTIONAL,
  dscpValue                      INTEGER(0..63) OPTIONAL
}

MediaTransportType ::= CHOICE {
  ip-UDP               NULL,
  ip-TCP               NULL,
  atm-AAL5-UNIDIR      NULL, -- virtual circuits used as unidirectional
  atm-AAL5-BIDIR       NULL, -- virtual circuits used as bidirectional
  ...,
  atm-AAL5-compressed  SEQUENCE {variable-delta  BOOLEAN,
                                 ...}
}

MediaChannelCapability ::= SEQUENCE {
  mediaTransport  MediaTransportType OPTIONAL,
  ...
}

TransportCapability ::= SEQUENCE {
  nonStandard               NonStandardParameter OPTIONAL,
  qOSCapabilities           SEQUENCE SIZE (1..256) OF QOSCapability OPTIONAL,
  mediaChannelCapabilities
    SEQUENCE SIZE (1..256) OF MediaChannelCapability OPTIONAL,
  ...
}

RedundancyEncodingCapability ::= SEQUENCE {
  redundancyEncodingMethod  RedundancyEncodingMethod,
  primaryEncoding           CapabilityTableEntryNumber,
  secondaryEncoding
    SEQUENCE SIZE (1..256) OF CapabilityTableEntryNumber OPTIONAL,
  -- must be Audio, Video, or Data capabilities, not derived
  -- capabilities; redundancy order is inferred from number of
  -- secondary encodings
  ...
}

RedundancyEncodingMethod ::= CHOICE {
  nonStandard                     NonStandardParameter,
  rtpAudioRedundancyEncoding      NULL,
  ...,
  rtpH263VideoRedundancyEncoding  RTPH263VideoRedundancyEncoding
}

RTPH263VideoRedundancyEncoding ::= SEQUENCE {
  numberOfThreads          INTEGER(1..16),
  framesBetweenSyncPoints  INTEGER(1..256),
  frameToThreadMapping
    CHOICE {roundrobin  NULL,
            custom
              SEQUENCE SIZE (1..256) OF RTPH263VideoRedundancyFrameMapping,
            -- empty SEQUENCE for capability negotiation
            -- meaningful contents only OpenLogicalChannel
            ...},
  containedThreads         SEQUENCE SIZE (1..256) OF INTEGER(0..15) OPTIONAL,
  -- only used for opening of logical channels
  ...
}

RTPH263VideoRedundancyFrameMapping ::= SEQUENCE {
  threadNumber   INTEGER(0..15),
  frameSequence  SEQUENCE SIZE (1..256) OF INTEGER(0..255),
  ...
}

MultipointCapability ::= SEQUENCE {
  multicastCapability          BOOLEAN,
  multiUniCastConference       BOOLEAN,
  mediaDistributionCapability  SEQUENCE OF MediaDistributionCapability,
  ...
}

MediaDistributionCapability ::= SEQUENCE {
  centralizedControl  BOOLEAN,
  distributedControl  BOOLEAN, -- for further study in

  -- ITU-T Rec. H.323
  centralizedAudio    BOOLEAN,
  distributedAudio    BOOLEAN,
  centralizedVideo    BOOLEAN,
  distributedVideo    BOOLEAN,
  centralizedData     SEQUENCE OF DataApplicationCapability OPTIONAL,
  distributedData     SEQUENCE OF DataApplicationCapability OPTIONAL,
  -- for further study in
  -- ITU-T Rec. H.323
  ...
}

-- =============================================================================
-- Capability exchange definitions: Video capabilities
-- =============================================================================
VideoCapability ::= CHOICE {
  nonStandard              NonStandardParameter,
  h261VideoCapability      H261VideoCapability,
  h262VideoCapability      H262VideoCapability,
  h263VideoCapability      H263VideoCapability,
  is11172VideoCapability   IS11172VideoCapability,
  ...,
  genericVideoCapability   GenericCapability,
  extendedVideoCapability  ExtendedVideoCapability
}

ExtendedVideoCapability ::= SEQUENCE {
  videoCapability           SEQUENCE OF VideoCapability,
  videoCapabilityExtension  SEQUENCE OF GenericCapability OPTIONAL,
  ...
}

H261VideoCapability ::= SEQUENCE {
  qcifMPI                            INTEGER(1..4) OPTIONAL, -- units 1/29.97 Hz
  cifMPI                             INTEGER(1..4) OPTIONAL, -- units 1/29.97 Hz
  temporalSpatialTradeOffCapability  BOOLEAN,
  maxBitRate                         INTEGER(1..19200), -- units of

  -- 100 bit/s
  stillImageTransmission             BOOLEAN, -- Annex D/H.261
  ...,
  videoBadMBsCap                     BOOLEAN
}

H262VideoCapability ::= SEQUENCE {
  profileAndLevel-SPatML         BOOLEAN,
  profileAndLevel-MPatLL         BOOLEAN,
  profileAndLevel-MPatML         BOOLEAN,
  profileAndLevel-MPatH-14       BOOLEAN,
  profileAndLevel-MPatHL         BOOLEAN,
  profileAndLevel-SNRatLL        BOOLEAN,
  profileAndLevel-SNRatML        BOOLEAN,
  profileAndLevel-SpatialatH-14  BOOLEAN,
  profileAndLevel-HPatML         BOOLEAN,
  profileAndLevel-HPatH-14       BOOLEAN,
  profileAndLevel-HPatHL         BOOLEAN,
  videoBitRate                   INTEGER(0..1073741823) OPTIONAL, -- units 400 bit/s
  vbvBufferSize                  INTEGER(0..262143) OPTIONAL, -- units 16 384 bits
  samplesPerLine                 INTEGER(0..16383) OPTIONAL, -- units samples/line
  linesPerFrame                  INTEGER(0..16383) OPTIONAL, -- units lines/frame
  framesPerSecond                INTEGER(0..15) OPTIONAL, -- frame_rate_code
  luminanceSampleRate            INTEGER(0..4294967295) OPTIONAL, -- units samples/s
  ...,
  videoBadMBsCap                 BOOLEAN
}

H263VideoCapability ::= SEQUENCE {
  sqcifMPI                           INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  qcifMPI                            INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  cifMPI                             INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  cif4MPI                            INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  cif16MPI                           INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  maxBitRate                         INTEGER(1..192400), -- units 100 bit/s
  unrestrictedVector                 BOOLEAN,
  arithmeticCoding                   BOOLEAN,
  advancedPrediction                 BOOLEAN,
  pbFrames                           BOOLEAN,
  temporalSpatialTradeOffCapability  BOOLEAN,
  hrd-B                              INTEGER(0..524287) OPTIONAL, -- units 128 bits
  bppMaxKb                           INTEGER(0..65535) OPTIONAL, -- units 1024 bits
  ...,
  slowSqcifMPI                       INTEGER(1..3600) OPTIONAL, --  units seconds/frame
  slowQcifMPI                        INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  slowCifMPI                         INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  slowCif4MPI                        INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  slowCif16MPI                       INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  errorCompensation                  BOOLEAN,
  enhancementLayerInfo               EnhancementLayerInfo OPTIONAL,
  h263Options                        H263Options OPTIONAL
}

EnhancementLayerInfo ::= SEQUENCE {
  baseBitRateConstrained  BOOLEAN,
  snrEnhancement          SET SIZE (1..14) OF EnhancementOptions OPTIONAL,
  spatialEnhancement      SET SIZE (1..14) OF EnhancementOptions OPTIONAL,
  bPictureEnhancement     SET SIZE (1..14) OF BEnhancementParameters OPTIONAL,
  ...
}

BEnhancementParameters ::= SEQUENCE {
  enhancementOptions  EnhancementOptions,
  numberOfBPictures   INTEGER(1..64),
  ...
}

EnhancementOptions ::= SEQUENCE {
  sqcifMPI                           INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  qcifMPI                            INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  cifMPI                             INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  cif4MPI                            INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  cif16MPI                           INTEGER(1..32) OPTIONAL, -- units 1/29.97 Hz
  maxBitRate                         INTEGER(1..192400), -- units 100 bit/s
  unrestrictedVector                 BOOLEAN,
  arithmeticCoding                   BOOLEAN,
  temporalSpatialTradeOffCapability  BOOLEAN,
  slowSqcifMPI                       INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  slowQcifMPI                        INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  slowCifMPI                         INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  slowCif4MPI                        INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  slowCif16MPI                       INTEGER(1..3600) OPTIONAL, -- units seconds/frame
  errorCompensation                  BOOLEAN,
  h263Options                        H263Options OPTIONAL,
  ...
}

H263Options ::= SEQUENCE {
  advancedIntraCodingMode             BOOLEAN,
  deblockingFilterMode                BOOLEAN,
  improvedPBFramesMode                BOOLEAN,
  unlimitedMotionVectors              BOOLEAN,
  fullPictureFreeze                   BOOLEAN,
  partialPictureFreezeAndRelease      BOOLEAN,
  resizingPartPicFreezeAndRelease     BOOLEAN,
  fullPictureSnapshot                 BOOLEAN,
  partialPictureSnapshot              BOOLEAN,
  videoSegmentTagging                 BOOLEAN,
  progressiveRefinement               BOOLEAN,
  dynamicPictureResizingByFour        BOOLEAN,
  dynamicPictureResizingSixteenthPel  BOOLEAN,
  dynamicWarpingHalfPel               BOOLEAN,
  dynamicWarpingSixteenthPel          BOOLEAN,
  independentSegmentDecoding          BOOLEAN,
  slicesInOrder-NonRect               BOOLEAN,
  slicesInOrder-Rect                  BOOLEAN,
  slicesNoOrder-NonRect               BOOLEAN,
  slicesNoOrder-Rect                  BOOLEAN,
  alternateInterVLCMode               BOOLEAN,
  modifiedQuantizationMode            BOOLEAN,
  reducedResolutionUpdate             BOOLEAN,
  transparencyParameters              TransparencyParameters OPTIONAL,
  separateVideoBackChannel            BOOLEAN,
  refPictureSelection                 RefPictureSelection OPTIONAL,
  customPictureClockFrequency
    SET SIZE (1..16) OF CustomPictureClockFrequency OPTIONAL,
  customPictureFormat
    SET SIZE (1..16) OF CustomPictureFormat OPTIONAL,
  modeCombos
    SET SIZE (1..16) OF H263VideoModeCombos OPTIONAL,
  ...,
  videoBadMBsCap                      BOOLEAN,
  h263Version3Options                 H263Version3Options
}

TransparencyParameters ::= SEQUENCE {
  presentationOrder  INTEGER(1..256),
  offset-x           INTEGER(-262144..262143), -- 1/8 pixels
  offset-y           INTEGER(-262144..262143), -- 1/8 pixels
  scale-x            INTEGER(1..255),
  scale-y            INTEGER(1..255),
  ...
}

RefPictureSelection ::= SEQUENCE {
  additionalPictureMemory
    SEQUENCE {sqcifAdditionalPictureMemory   INTEGER(1..256) OPTIONAL, -- units frame--
              qcifAdditionalPictureMemory    INTEGER(1..256) OPTIONAL, -- units frame--
              cifAdditionalPictureMemory     INTEGER(1..256) OPTIONAL, -- units frame--
              cif4AdditionalPictureMemory    INTEGER(1..256) OPTIONAL, -- units frame--
              cif16AdditionalPictureMemory   INTEGER(1..256) OPTIONAL, -- units frame--
              bigCpfAdditionalPictureMemory  INTEGER(1..256) OPTIONAL, -- units frame--
              ...} OPTIONAL,
  videoMux                       BOOLEAN,
  videoBackChannelSend
    CHOICE {none                  NULL,
            ackMessageOnly        NULL,
            nackMessageOnly       NULL,
            ackOrNackMessageOnly  NULL,
            ackAndNackMessage     NULL,
            ...},
  ...,
  enhancedReferencePicSelect
    SEQUENCE {subPictureRemovalParameters
                SEQUENCE {mpuHorizMBs     INTEGER(1..128),
                          mpuVertMBs      INTEGER(1..72),
                          mpuTotalNumber  INTEGER(1..65536),
                          ...} OPTIONAL,
              ...}
}

CustomPictureClockFrequency ::= SEQUENCE {
  clockConversionCode  INTEGER(1000..1001),
  clockDivisor         INTEGER(1..127),
  sqcifMPI             INTEGER(1..2048) OPTIONAL,
  qcifMPI              INTEGER(1..2048) OPTIONAL,
  cifMPI               INTEGER(1..2048) OPTIONAL,
  cif4MPI              INTEGER(1..2048) OPTIONAL,
  cif16MPI             INTEGER(1..2048) OPTIONAL,
  ...
}

CustomPictureFormat ::= SEQUENCE {
  maxCustomPictureWidth   INTEGER(1..2048), -- units 4 pixels
  maxCustomPictureHeight  INTEGER(1..2048), -- units 4 pixels
  minCustomPictureWidth   INTEGER(1..2048), -- units 4 pixels
  minCustomPictureHeight  INTEGER(1..2048), -- units 4 pixels
  mPI
    SEQUENCE {standardMPI  INTEGER(1..31) OPTIONAL,
              customPCF
                SET SIZE (1..16) OF
                  SEQUENCE {clockConversionCode  INTEGER(1000..1001),
                            clockDivisor         INTEGER(1..127),
                            customMPI            INTEGER(1..2048),
                            ...} OPTIONAL,
              ...},
  pixelAspectInformation
    CHOICE {anyPixelAspectRatio  BOOLEAN,
            pixelAspectCode      SET SIZE (1..14) OF INTEGER(1..14),
            extendedPAR
              SET SIZE (1..256) OF
                SEQUENCE {width   INTEGER(1..255),
                          height  INTEGER(1..255),
                          ...},
            ...},
  ...
}

H263VideoModeCombos ::= SEQUENCE {
  h263VideoUncoupledModes  H263ModeComboFlags,
  h263VideoCoupledModes    SET SIZE (1..16) OF H263ModeComboFlags,
  ...
}

H263ModeComboFlags ::= SEQUENCE {
  unrestrictedVector                  BOOLEAN,
  arithmeticCoding                    BOOLEAN,
  advancedPrediction                  BOOLEAN,
  pbFrames                            BOOLEAN,
  advancedIntraCodingMode             BOOLEAN,
  deblockingFilterMode                BOOLEAN,
  unlimitedMotionVectors              BOOLEAN,
  slicesInOrder-NonRect               BOOLEAN,
  slicesInOrder-Rect                  BOOLEAN,
  slicesNoOrder-NonRect               BOOLEAN,
  slicesNoOrder-Rect                  BOOLEAN,
  improvedPBFramesMode                BOOLEAN,
  referencePicSelect                  BOOLEAN,
  dynamicPictureResizingByFour        BOOLEAN,
  dynamicPictureResizingSixteenthPel  BOOLEAN,
  dynamicWarpingHalfPel               BOOLEAN,
  dynamicWarpingSixteenthPel          BOOLEAN,
  reducedResolutionUpdate             BOOLEAN,
  independentSegmentDecoding          BOOLEAN,
  alternateInterVLCMode               BOOLEAN,
  modifiedQuantizationMode            BOOLEAN,
  ...,
  enhancedReferencePicSelect          BOOLEAN,
  h263Version3Options                 H263Version3Options
}

H263Version3Options ::= SEQUENCE {
  dataPartitionedSlices            BOOLEAN,
  fixedPointIDCT0                  BOOLEAN,
  interlacedFields                 BOOLEAN,
  currentPictureHeaderRepetition   BOOLEAN,
  previousPictureHeaderRepetition  BOOLEAN,
  nextPictureHeaderRepetition      BOOLEAN,
  pictureNumber                    BOOLEAN,
  spareReferencePictures           BOOLEAN,
  ...
}

IS11172VideoCapability ::= SEQUENCE {
  constrainedBitstream  BOOLEAN,
  videoBitRate          INTEGER(0..1073741823) OPTIONAL, -- units 400 bit/s
  vbvBufferSize         INTEGER(0..262143) OPTIONAL, -- units 16 384 bits
  samplesPerLine        INTEGER(0..16383) OPTIONAL, -- units samples/line
  linesPerFrame         INTEGER(0..16383) OPTIONAL, -- units lines/frame
  pictureRate           INTEGER(0..15) OPTIONAL,
  luminanceSampleRate   INTEGER(0..4294967295) OPTIONAL, -- units samples/s
  ...,
  videoBadMBsCap        BOOLEAN
}

-- =============================================================================
-- Capability exchange definitions: Audio capabilities
-- =============================================================================
-- For an H.222 multiplex, the integers indicate the size of the STD buffer
-- in units of 256 octets
-- For an H.223 multiplex, the integers indicate the maximum number of audio
-- frames per AL-SDU
-- For an H.225.0 multiplex, the integers indicate the maximum number of audio
-- frames per packet
AudioCapability ::= CHOICE {
  nonStandard             NonStandardParameter,
  g711Alaw64k             INTEGER(1..256),
  g711Alaw56k             INTEGER(1..256),
  g711Ulaw64k             INTEGER(1..256),
  g711Ulaw56k             INTEGER(1..256),
  g722-64k                INTEGER(1..256),
  g722-56k                INTEGER(1..256),
  g722-48k                INTEGER(1..256),
  g7231
    SEQUENCE {maxAl-sduAudioFrames  INTEGER(1..256),
              silenceSuppression    BOOLEAN},
  g728                    INTEGER(1..256),
  g729                    INTEGER(1..256),
  g729AnnexA              INTEGER(1..256),
  is11172AudioCapability  IS11172AudioCapability,
  is13818AudioCapability  IS13818AudioCapability,
  ...,
  g729wAnnexB             INTEGER(1..256),
  g729AnnexAwAnnexB       INTEGER(1..256),
  g7231AnnexCCapability   G7231AnnexCCapability,
  gsmFullRate             GSMAudioCapability,
  gsmHalfRate             GSMAudioCapability,
  gsmEnhancedFullRate     GSMAudioCapability,
  genericAudioCapability  GenericCapability,
  g729Extensions          G729Extensions,
  vbd                     VBDCapability,
  audioTelephonyEvent     NoPTAudioTelephonyEventCapability,
  audioTone               NoPTAudioToneCapability
}

G729Extensions ::= SEQUENCE {
  audioUnit  INTEGER(1..256) OPTIONAL,
  annexA     BOOLEAN,
  annexB     BOOLEAN,
  annexD     BOOLEAN,
  annexE     BOOLEAN,
  annexF     BOOLEAN,
  annexG     BOOLEAN,
  annexH     BOOLEAN,
  ...
}

G7231AnnexCCapability ::= SEQUENCE {
  maxAl-sduAudioFrames  INTEGER(1..256),
  silenceSuppression    BOOLEAN,
  g723AnnexCAudioMode
    SEQUENCE {highRateMode0  INTEGER(27..78), -- units octets--
              highRateMode1  INTEGER(27..78), -- units octets--
              lowRateMode0   INTEGER(23..66), -- units octets--
              lowRateMode1   INTEGER(23..66), -- units octets--
              sidMode0       INTEGER(6..17), -- units octets--
              sidMode1       INTEGER(6..17), -- units octets--
              ...} OPTIONAL,
  ...
}

IS11172AudioCapability ::= SEQUENCE {
  audioLayer1        BOOLEAN,
  audioLayer2        BOOLEAN,
  audioLayer3        BOOLEAN,
  audioSampling32k   BOOLEAN,
  audioSampling44k1  BOOLEAN,
  audioSampling48k   BOOLEAN,
  singleChannel      BOOLEAN,
  twoChannels        BOOLEAN,
  bitRate            INTEGER(1..448), -- units kbit/s
  ...
}

IS13818AudioCapability ::= SEQUENCE {
  audioLayer1              BOOLEAN,
  audioLayer2              BOOLEAN,
  audioLayer3              BOOLEAN,
  audioSampling16k         BOOLEAN,
  audioSampling22k05       BOOLEAN,
  audioSampling24k         BOOLEAN,
  audioSampling32k         BOOLEAN,
  audioSampling44k1        BOOLEAN,
  audioSampling48k         BOOLEAN,
  singleChannel            BOOLEAN,
  twoChannels              BOOLEAN,
  threeChannels2-1         BOOLEAN,
  threeChannels3-0         BOOLEAN,
  fourChannels2-0-2-0      BOOLEAN,
  fourChannels2-2          BOOLEAN,
  fourChannels3-1          BOOLEAN,
  fiveChannels3-0-2-0      BOOLEAN,
  fiveChannels3-2          BOOLEAN,
  lowFrequencyEnhancement  BOOLEAN,
  multilingual             BOOLEAN,
  bitRate                  INTEGER(1..1130), -- units kbit/s
  ...
}

GSMAudioCapability ::= SEQUENCE {
  audioUnitSize  INTEGER(1..256),
  comfortNoise   BOOLEAN,
  scrambled      BOOLEAN,
  ...
}

VBDCapability ::= SEQUENCE {type  AudioCapability, -- shall not be "vbd"
                            ...
}

-- =============================================================================
-- Capability exchange definitions: Data capabilities
-- =============================================================================
DataApplicationCapability ::= SEQUENCE {
  application
    CHOICE {nonStandard            NonStandardParameter,
            t120                   DataProtocolCapability,
            dsm-cc                 DataProtocolCapability,
            userData               DataProtocolCapability,
            t84
              SEQUENCE {t84Protocol  DataProtocolCapability,
                        t84Profile   T84Profile},
            t434                   DataProtocolCapability,
            h224                   DataProtocolCapability,
            nlpid
              SEQUENCE {nlpidProtocol  DataProtocolCapability,
                        nlpidData      OCTET STRING},
            dsvdControl            NULL,
            h222DataPartitioning   DataProtocolCapability,
            ...,
            t30fax                 DataProtocolCapability,
            t140                   DataProtocolCapability,
            t38fax
              SEQUENCE {t38FaxProtocol  DataProtocolCapability,
                        t38FaxProfile   T38FaxProfile},
            genericDataCapability  GenericCapability},
  maxBitRate   INTEGER(0..4294967295), -- units 100 bit/s
  ...
}

DataProtocolCapability ::= CHOICE {
  nonStandard                NonStandardParameter,
  v14buffered                NULL,
  v42lapm                    NULL, -- may negotiate to V.42 bis
  hdlcFrameTunnelling        NULL,
  h310SeparateVCStack        NULL,
  h310SingleVCStack          NULL,
  transparent                NULL,
  ...,
  segmentationAndReassembly  NULL,
  hdlcFrameTunnelingwSAR     NULL,
  v120                       NULL, -- as in H.230
  separateLANStack           NULL,
  v76wCompression
    CHOICE {transmitCompression            CompressionType,
            receiveCompression             CompressionType,
            transmitAndReceiveCompression  CompressionType,
            ...},
  tcp                        NULL,
  udp                        NULL
}

CompressionType ::= CHOICE {v42bis  V42bis,
                            ...
}

V42bis ::= SEQUENCE {
  numberOfCodewords    INTEGER(1..65536),
  maximumStringLength  INTEGER(1..256),
  ...
}

T84Profile ::= CHOICE {
  t84Unrestricted  NULL,
  t84Restricted
    SEQUENCE {qcif              BOOLEAN,
              cif               BOOLEAN,
              ccir601Seq        BOOLEAN,
              ccir601Prog       BOOLEAN,
              hdtvSeq           BOOLEAN,
              hdtvProg          BOOLEAN,
              g3FacsMH200x100   BOOLEAN,
              g3FacsMH200x200   BOOLEAN,
              g4FacsMMR200x100  BOOLEAN,
              g4FacsMMR200x200  BOOLEAN,
              jbig200x200Seq    BOOLEAN,
              jbig200x200Prog   BOOLEAN,
              jbig300x300Seq    BOOLEAN,
              jbig300x300Prog   BOOLEAN,
              digPhotoLow       BOOLEAN,
              digPhotoMedSeq    BOOLEAN,
              digPhotoMedProg   BOOLEAN,
              digPhotoHighSeq   BOOLEAN,
              digPhotoHighProg  BOOLEAN,
              ...}
}

T38FaxProfile ::= SEQUENCE {
  fillBitRemoval           BOOLEAN,
  transcodingJBIG          BOOLEAN,
  transcodingMMR           BOOLEAN,
  ...,
  version                  INTEGER(0..255),
  -- Version 0, the default, refers to
  -- T.38 (2005)
  t38FaxRateManagement     T38FaxRateManagement,
  -- The default Data Rate Management is
  -- determined by the choice of
  -- DataProtocolCapability
  t38FaxUdpOptions         T38FaxUdpOptions OPTIONAL,
  -- For UDP, t38UDPRedundancy is the default
  t38FaxTcpOptions         T38FaxTcpOptions OPTIONAL
}

T38FaxRateManagement ::= CHOICE {
  localTCF        NULL,
  transferredTCF  NULL,
  ...
}

T38FaxUdpOptions ::= SEQUENCE {
  t38FaxMaxBuffer    INTEGER OPTIONAL,
  t38FaxMaxDatagram  INTEGER OPTIONAL,
  t38FaxUdpEC
    CHOICE {t38UDPFEC         NULL,
            t38UDPRedundancy  NULL,
            ...}
}

T38FaxTcpOptions ::= SEQUENCE {t38TCPBidirectionalMode  BOOLEAN,
                               ...
}

-- =============================================================================
-- Encryption Capability Definitions
-- =============================================================================
EncryptionAuthenticationAndIntegrity ::= SEQUENCE {
  encryptionCapability              EncryptionCapability OPTIONAL,
  authenticationCapability          AuthenticationCapability OPTIONAL,
  integrityCapability               IntegrityCapability OPTIONAL,
  ...,
  genericH235SecurityCapability     GenericCapability OPTIONAL
}

EncryptionCapability ::= SEQUENCE SIZE (1..256) OF MediaEncryptionAlgorithm

MediaEncryptionAlgorithm ::= CHOICE {
  nonStandard  NonStandardParameter,
  algorithm    OBJECT IDENTIFIER, -- many defined

  -- in ISO/IEC 9979
  ...
}

AuthenticationCapability ::= SEQUENCE {
  nonStandard           NonStandardParameter OPTIONAL,
  ...,
  antiSpamAlgorithm     OBJECT IDENTIFIER OPTIONAL
}

IntegrityCapability ::= SEQUENCE {
  nonStandard  NonStandardParameter OPTIONAL,
  ...
}

-- =============================================================================
-- Capability Exchange Definitions: UserInput
-- =============================================================================
UserInputCapability ::= CHOICE {
  nonStandard                 SEQUENCE SIZE (1..16) OF NonStandardParameter,
  basicString                 NULL, -- alphanumeric
  iA5String                   NULL, -- alphanumeric
  generalString               NULL, -- alphanumeric
  dtmf                        NULL, -- supports dtmf using signal

  -- and signalUpdate
  hookflash                   NULL, -- supports hookflash using signal
  ...,
  extendedAlphanumeric        NULL,
  encryptedBasicString        NULL, -- encrypted Basic string in

  -- encryptedAlphanumeric
  encryptedIA5String          NULL, -- encrypted IA5 string in

  -- encryptedSignalType
  encryptedGeneralString      NULL, -- encrypted general string in

  -- extendedAlphanumeric.encryptedalphanumeric
  secureDTMF                  NULL, -- secure DTMF using encryptedSignalType
  genericUserInputCapability  GenericCapability
}

-- =============================================================================
-- Capability Exchange Definitions: Conference
-- =============================================================================
ConferenceCapability ::= SEQUENCE {
  nonStandardData
    SEQUENCE OF NonStandardParameter OPTIONAL,
  chairControlCapability                BOOLEAN,
  ...,
  videoIndicateMixingCapability         BOOLEAN,
  multipointVisualizationCapability     BOOLEAN OPTIONAL -- same as H.230 MVC
}

-- =============================================================================
-- Capability Exchange Definitions: Generic Capability
-- =============================================================================
GenericCapability ::= SEQUENCE {
  capabilityIdentifier  CapabilityIdentifier,
  maxBitRate            INTEGER(0..4294967295) OPTIONAL,
  -- Units 100 bit/s
  collapsing            SEQUENCE OF GenericParameter OPTIONAL,
  nonCollapsing         SEQUENCE OF GenericParameter OPTIONAL,
  nonCollapsingRaw      OCTET STRING OPTIONAL,
  -- Typically contains ASN.1
  -- PER encoded data describing capability
  transport             DataProtocolCapability OPTIONAL,
  ...
}

CapabilityIdentifier ::= CHOICE {
  standard         OBJECT IDENTIFIER,
  -- e.g., { itu-t (0) recommendation (0) h (8) 267
  -- version (0) 2 subIdentifier (0)}
  h221NonStandard  NonStandardParameter,
  uuid             OCTET STRING(SIZE (16)),
  domainBased      IA5String(SIZE (1..64)),
  ...
}

-- NOTE - The ranges of parameter values have been selected to ensure that the
-- GenericParameter preamble, standard part of ParameterIdentifier and the
-- encoding of that choice, and the preamble of ParameterValue to fit into
-- 2 octets.
GenericParameter ::= SEQUENCE {
  parameterIdentifier  ParameterIdentifier,
  parameterValue       ParameterValue,
  supersedes           SEQUENCE OF ParameterIdentifier OPTIONAL,
  ...
}

ParameterIdentifier ::= CHOICE {
  standard         INTEGER(0..127), -- Assigned by

  -- Capability
  -- specifications
  h221NonStandard  NonStandardParameter, -- N.B.

  -- NonStandardIdentifier
  -- is not sufficient in
  -- this case
  uuid             OCTET STRING(SIZE (16)), -- For non-

  -- standard
  domainBased      IA5String(SIZE (1..64)),
  ...
}

ParameterValue ::= CHOICE {
  logical           NULL, -- Only acceptable if

  -- all entities
  -- include this option
  booleanArray      INTEGER(0..255), -- array of 8 logical

  -- types
  unsignedMin       INTEGER(0..65535), -- Look for min

  -- common value
  unsignedMax       INTEGER(0..65535), -- Look for max

  -- common value
  unsigned32Min     INTEGER(0..4294967295), -- Look for min

  -- common value
  unsigned32Max     INTEGER(0..4294967295), -- Look for max

  -- common value
  octetString       OCTET STRING, -- non-collapsing

  -- octet string
  genericParameter  SEQUENCE OF GenericParameter,
  ...
}

-- =============================================================================
-- Capability Exchange Definitions: Multiplexed Stream Capability
-- =============================================================================
MultiplexedStreamCapability ::= SEQUENCE {
  multiplexFormat        MultiplexFormat,
  controlOnMuxStream     BOOLEAN,
  capabilityOnMuxStream  SET SIZE (1..256) OF AlternativeCapabilitySet OPTIONAL,
  ...
}

MultiplexFormat ::= CHOICE {
  nonStandard     NonStandardParameter,
  h222Capability  H222Capability,
  h223Capability  H223Capability,
  ...
}

-- =============================================================================
-- Capability Exchange Definitions: AudioTelephonyEventCapability and AudioToneCapability
--==============================================================================
AudioTelephonyEventCapability ::= SEQUENCE {
  dynamicRTPPayloadType  INTEGER(96..127),
  audioTelephoneEvent    GeneralString, -- As per <list of values>

  -- in RFC 4733
  ...
}

AudioToneCapability ::= SEQUENCE {dynamicRTPPayloadType  INTEGER(96..127),
                                  ...
}

-- The following definitions are as above but without a Payload Type field.
NoPTAudioTelephonyEventCapability ::= SEQUENCE {
  audioTelephoneEvent  GeneralString, -- As per <list of values>

  -- in RFC 4733
  ...
}

NoPTAudioToneCapability ::= SEQUENCE {...
}

-- =============================================================================
-- Capability Exchange Definitions: MultiplePayloadStreamCapability
-- =============================================================================
MultiplePayloadStreamCapability ::= SEQUENCE {
  capabilities  SET SIZE (1..256) OF AlternativeCapabilitySet,
  ...
}

-- =============================================================================
-- Capability Exchange Definitions: FECCapability
-- =============================================================================
DepFECCapability ::= CHOICE -- Deprecated, do not use
                      {
  rfc2733
    SEQUENCE {redundancyEncoding  BOOLEAN,
              separateStream
                SEQUENCE {separatePort  BOOLEAN,
                          samePort      BOOLEAN,
                          ...},
              ...},
  ...
}

FECCapability ::= SEQUENCE {
  protectedCapability  CapabilityTableEntryNumber,
  fecScheme
    -- identifies encoding scheme -- OBJECT IDENTIFIER OPTIONAL,
  rfc2733Format
    CHOICE {rfc2733rfc2198   -- RFC 2198 -- MaxRedundancy,
            rfc2733sameport  -- separate packet, same port -- MaxRedundancy,
            rfc2733diffport  -- separate packet and port -- MaxRedundancy
  } OPTIONAL,
  ...
}

MaxRedundancy ::= INTEGER(1..MAX)

-- =============================================================================
-- Logical channel signalling definitions
-- =============================================================================
-- "Forward" is used to refer to transmission in the direction from the terminal
-- making the original request for a logical channel to the other terminal, and
-- "reverse" is used to refer to the opposite direction of transmission, in the
-- case of a bidirectional channel request.
OpenLogicalChannel ::= SEQUENCE {
  forwardLogicalChannelNumber      LogicalChannelNumber,
  forwardLogicalChannelParameters
    SEQUENCE {portNumber                          INTEGER(0..65535) OPTIONAL,
              dataType                            DataType,
              multiplexParameters
                CHOICE {h222LogicalChannelParameters
                          H222LogicalChannelParameters,
                        h223LogicalChannelParameters
                          H223LogicalChannelParameters,
                        v76LogicalChannelParameters
                          V76LogicalChannelParameters,
                        ...,
                        h2250LogicalChannelParameters
                          H2250LogicalChannelParameters,
                        none                           NULL}, -- for use with Separate Stack when--
              -- multiplexParameters are not
              -- required or appropriate
              ...,
              forwardLogicalChannelDependency     LogicalChannelNumber OPTIONAL,
              -- also used to refer to the primary
              -- logical channel when using video
              -- redundancy coding
              replacementFor                      LogicalChannelNumber OPTIONAL
  },
  -- Used to specify the reverse channel for bidirectional open request
  reverseLogicalChannelParameters
    SEQUENCE {dataType                            DataType,
              multiplexParameters
                CHOICE {-- H.222 parameters are never present in reverse direction
                        h223LogicalChannelParameters
                          H223LogicalChannelParameters,
                        v76LogicalChannelParameters
                          V76LogicalChannelParameters,
                        ...,
                        h2250LogicalChannelParameters
                          H2250LogicalChannelParameters} OPTIONAL, -- Not present for H.222--
              ...,
              reverseLogicalChannelDependency     LogicalChannelNumber OPTIONAL,
              -- also used to refer to the primary logical channel when using
              -- video redundancy coding
              replacementFor                      LogicalChannelNumber OPTIONAL
  } OPTIONAL, -- Not present for unidirectional channel request
  ...,
  separateStack                    NetworkAccessParameters OPTIONAL,
  -- for Open responder to establish the stack
  encryptionSync                   EncryptionSync OPTIONAL,
  genericInformation               SEQUENCE OF GenericInformation OPTIONAL
}

-- generic information associated
-- with the message
LogicalChannelNumber ::= INTEGER(1..65535)

NetworkAccessParameters ::= SEQUENCE {
  distribution
    CHOICE {unicast    NULL,
            multicast  NULL, -- for further study in T.120--
            ...} OPTIONAL,
  networkAddress
    CHOICE {q2931Address      Q2931Address,
            e164Address       IA5String(SIZE (1..128))(FROM ("0123456789#*,")),
            localAreaAddress  TransportAddressH245,
            ...},
  associateConference    BOOLEAN,
  externalReference      OCTET STRING(SIZE (1..255)) OPTIONAL,
  ...,
  t120SetupProcedure
    CHOICE {originateCall  NULL,
            waitForCall    NULL,
            issueQuery     NULL,
            ...} OPTIONAL
}

Q2931Address ::= SEQUENCE {
  address
    CHOICE {internationalNumber  NumericString(SIZE (1..16)),
            nsapAddress          OCTET STRING(SIZE (1..20)),
            ...},
  subaddress  OCTET STRING(SIZE (1..20)) OPTIONAL,
  ...
}

V75Parameters ::= SEQUENCE {audioHeaderPresent  BOOLEAN,
                            ...
}

DataType ::= CHOICE {
  nonStandard            NonStandardParameter,
  nullData               NULL,
  videoData              VideoCapability,
  audioData              AudioCapability,
  data                   DataApplicationCapability,
  encryptionData         EncryptionMode,
  ...,
  h235Control            NonStandardParameter,
  h235Media              H235Media,
  multiplexedStream      MultiplexedStreamParameter,
  redundancyEncoding     RedundancyEncoding,
  multiplePayloadStream  MultiplePayloadStream,
  depFec                 DepFECData, -- Deprecated, do not use
  fec                    FECData
}

H235Media ::= SEQUENCE {
  encryptionAuthenticationAndIntegrity  EncryptionAuthenticationAndIntegrity,
  mediaType
    CHOICE {nonStandard            NonStandardParameter,
            videoData              VideoCapability,
            audioData              AudioCapability,
            data                   DataApplicationCapability,
            ...,
            redundancyEncoding     RedundancyEncoding,
            multiplePayloadStream  MultiplePayloadStream,
            depFec                 DepFECData, -- Deprecated, do not use--
            fec                    FECData},
  ...
}

MultiplexedStreamParameter ::= SEQUENCE {
  multiplexFormat     MultiplexFormat,
  controlOnMuxStream  BOOLEAN,
  ...
}

H222LogicalChannelParameters ::= SEQUENCE {
  resourceID          INTEGER(0..65535),
  subChannelID        INTEGER(0..8191),
  pcr-pid             INTEGER(0..8191) OPTIONAL,
  programDescriptors  OCTET STRING OPTIONAL,
  streamDescriptors   OCTET STRING OPTIONAL,
  ...
}

H223LogicalChannelParameters ::= SEQUENCE {
  adaptationLayerType
    CHOICE {nonStandard                NonStandardParameter,
            al1Framed                  NULL,
            al1NotFramed               NULL,
            al2WithoutSequenceNumbers  NULL,
            al2WithSequenceNumbers     NULL,
            al3
              SEQUENCE {controlFieldOctets  INTEGER(0..2),
                        sendBufferSize
                          -- units octets -- INTEGER(0..16777215)},
            ...,
            al1M                       H223AL1MParameters,
            al2M                       H223AL2MParameters,
            al3M                       H223AL3MParameters},
  segmentableFlag      BOOLEAN,
  ...
}

H223AL1MParameters ::= SEQUENCE {
  transferMode         CHOICE {framed    NULL,
                               unframed  NULL,
                               ...},
  headerFEC            CHOICE {sebch16-7   NULL,
                               golay24-12  NULL,
                               ...},
  crcLength
    CHOICE {crc4bit     NULL,
            crc12bit    NULL,
            crc20bit    NULL,
            crc28bit    NULL,
            ...,
            crc8bit     NULL,
            crc16bit    NULL,
            crc32bit    NULL,
            crcNotUsed  NULL},
  rcpcCodeRate         INTEGER(8..32),
  arqType
    CHOICE {noArq      NULL,
            typeIArq   H223AnnexCArqParameters,
            typeIIArq  H223AnnexCArqParameters,
            ...},
  alpduInterleaving    BOOLEAN,
  alsduSplitting       BOOLEAN,
  ...,
  rsCodeCorrection     INTEGER(0..127) OPTIONAL
}

H223AL2MParameters ::= SEQUENCE {
  headerFEC          CHOICE {sebch16-5   NULL,
                             golay24-12  NULL,
                             ...},
  alpduInterleaving  BOOLEAN,
  ...
}

H223AL3MParameters ::= SEQUENCE {
  headerFormat         CHOICE {sebch16-7   NULL,
                               golay24-12  NULL,
                               ...},
  crcLength
    CHOICE {crc4bit     NULL,
            crc12bit    NULL,
            crc20bit    NULL,
            crc28bit    NULL,
            ...,
            crc8bit     NULL,
            crc16bit    NULL,
            crc32bit    NULL,
            crcNotUsed  NULL},
  rcpcCodeRate         INTEGER(8..32),
  arqType
    CHOICE {noArq      NULL,
            typeIArq   H223AnnexCArqParameters,
            typeIIArq  H223AnnexCArqParameters,
            ...},
  alpduInterleaving    BOOLEAN,
  ...,
  rsCodeCorrection     INTEGER(0..127) OPTIONAL
}

H223AnnexCArqParameters ::= SEQUENCE {
  numberOfRetransmissions
    CHOICE {finite    INTEGER(0..16),
            infinite  NULL,
            ...},
  sendBufferSize           INTEGER(0..16777215), -- units octets
  ...
}

V76LogicalChannelParameters ::= SEQUENCE {
  hdlcParameters  V76HDLCParameters,
  suspendResume
    CHOICE {noSuspendResume         NULL,
            suspendResumewAddress   NULL,
            suspendResumewoAddress  NULL,
            ...},
  uIH             BOOLEAN,
  mode
    CHOICE {eRM
              SEQUENCE {windowSize  INTEGER(1..127),
                        recovery
                          CHOICE {rej    NULL,
                                  sREJ   NULL,
                                  mSREJ  NULL,
                                  ...},
                        ...},
            uNERM  NULL,
            ...},
  v75Parameters   V75Parameters,
  ...
}

V76HDLCParameters ::= SEQUENCE {
  crcLength              CRCLength,
  n401                   INTEGER(1..4095),
  loopbackTestProcedure  BOOLEAN,
  ...
}

CRCLength ::= CHOICE {crc8bit   NULL,
                      crc16bit  NULL,
                      crc32bit  NULL,
                      ...
}

H2250LogicalChannelParameters ::= SEQUENCE {
  nonStandard                     SEQUENCE OF NonStandardParameter OPTIONAL,
  sessionID                       INTEGER(0..255),
  associatedSessionID             INTEGER(1..255) OPTIONAL,
  mediaChannel                    TransportAddressH245 OPTIONAL,
  mediaGuaranteedDelivery         BOOLEAN OPTIONAL,
  mediaControlChannel             TransportAddressH245 OPTIONAL, -- reverse

  -- RTCP channel
  mediaControlGuaranteedDelivery  BOOLEAN OPTIONAL,
  silenceSuppression              BOOLEAN OPTIONAL,
  destination                     TerminalLabel OPTIONAL,
  dynamicRTPPayloadType           INTEGER(96..127) OPTIONAL,
  mediaPacketization
    CHOICE {h261aVideoPacketization  NULL,
            ...,
            rtpPayloadType           RTPPayloadType} OPTIONAL,
  ...,
  transportCapability             TransportCapability OPTIONAL,
  redundancyEncoding              RedundancyEncoding OPTIONAL,
  source                          TerminalLabel OPTIONAL
}

RTPPayloadType ::= SEQUENCE {
  payloadDescriptor
    CHOICE {nonStandardIdentifier  NonStandardParameter,
            rfc-number             INTEGER(1..32768, ...),
            oid                    OBJECT IDENTIFIER,
            ...},
  payloadType        INTEGER(0..127) OPTIONAL,
  ...
}

RedundancyEncoding ::= SEQUENCE {
  redundancyEncodingMethod  RedundancyEncodingMethod,
  secondaryEncoding         DataType OPTIONAL, -- depends on method
  ...,
  -- The sequence below may be used in place of the above secondaryEncoding field
  rtpRedundancyEncoding
    SEQUENCE {primary    RedundancyEncodingElement OPTIONAL,
              -- Present when redundancyEncoding
              -- is selected as the dataType
              -- in an OpenLogicalChannel or
              -- as part of a MultiplePayloadSteam
              secondary  SEQUENCE OF RedundancyEncodingElement OPTIONAL,
              ...} OPTIONAL
}

RedundancyEncodingElement ::= SEQUENCE {
  dataType     DataType,
  payloadType  INTEGER(0..127) OPTIONAL,
  ...
}

MultiplePayloadStream ::= SEQUENCE {
  elements  SEQUENCE OF MultiplePayloadStreamElement,
  ...
}

MultiplePayloadStreamElement ::= SEQUENCE {
  dataType     DataType,
  payloadType  INTEGER(0..127) OPTIONAL,
  ...
}

DepFECData ::= CHOICE -- Deprecated, do not use
                {
  rfc2733
    SEQUENCE {mode
                CHOICE {redundancyEncoding  NULL,
                        separateStream
                          CHOICE {differentPort
                                    SEQUENCE {protectedSessionID
                                                INTEGER(1..255),
                                              protectedPayloadType
                                                INTEGER(0..127) OPTIONAL,
                                              ...},
                                  samePort
                                    SEQUENCE {protectedPayloadType
                                                INTEGER(0..127),
                                              ...},
                                  ...},
                        ...},
              ...}
}

FECData ::= CHOICE {
  rfc2733
    SEQUENCE {protectedPayloadType  INTEGER(0..127),
              fecScheme             OBJECT IDENTIFIER OPTIONAL,
              pktMode
                CHOICE {rfc2198coding    NULL,
                        rfc2733sameport  SEQUENCE {...},
                        rfc2733diffport
                          SEQUENCE {protectedChannel  LogicalChannelNumber,
                                    ...},
                        ...},
              ...},
  ...
}

TransportAddressH245 ::= CHOICE {
  unicastAddress    UnicastAddress,
  multicastAddress  MulticastAddress,
  ...
}

UnicastAddress ::= CHOICE {
  iPAddress
    SEQUENCE {network         OCTET STRING(SIZE (4)),
              tsapIdentifier  INTEGER(0..65535),
              ...},
  iPXAddress
    SEQUENCE {node            OCTET STRING(SIZE (6)),
              netnum          OCTET STRING(SIZE (4)),
              tsapIdentifier  OCTET STRING(SIZE (2)),
              ...},
  iP6Address
    SEQUENCE {network         OCTET STRING(SIZE (16)),
              tsapIdentifier  INTEGER(0..65535),
              ...},
  netBios               OCTET STRING(SIZE (16)),
  iPSourceRouteAddress
    SEQUENCE {routing         CHOICE {strict  NULL,
                                      loose   NULL},
              network         OCTET STRING(SIZE (4)),
              tsapIdentifier  INTEGER(0..65535),
              route           SEQUENCE OF OCTET STRING(SIZE (4)),
              ...},
  ...,
  nsap                  OCTET STRING(SIZE (1..20)),
  nonStandardAddress    NonStandardParameter
}

MulticastAddress ::= CHOICE {
  iPAddress
    SEQUENCE {network         OCTET STRING(SIZE (4)),
              tsapIdentifier  INTEGER(0..65535),
              ...},
  iP6Address
    SEQUENCE {network         OCTET STRING(SIZE (16)),
              tsapIdentifier  INTEGER(0..65535),
              ...},
  ...,
  nsap                OCTET STRING(SIZE (1..20)),
  nonStandardAddress  NonStandardParameter
}

EncryptionSync ::=
  SEQUENCE
   -- used to supply new key and synchronization point
  {
  nonStandard          NonStandardParameter OPTIONAL,
  synchFlag            INTEGER(0..255), -- may need to be larger

  -- for H.324, etc.
  -- shall be the Dynamic
  -- Payload# for H.323
  h235Key              OCTET STRING(SIZE (1..65535)), -- H.235.0

  -- encoded value
  escrowentry          SEQUENCE SIZE (1..256) OF EscrowData OPTIONAL,
  ...,
  genericParameter     GenericParameter OPTIONAL
}

EscrowData ::= SEQUENCE {
  escrowID     OBJECT IDENTIFIER,
  escrowValue  BIT STRING(SIZE (1..65535)),
  ...
}

OpenLogicalChannelAck ::= SEQUENCE {
  forwardLogicalChannelNumber       LogicalChannelNumber,
  reverseLogicalChannelParameters
    SEQUENCE {reverseLogicalChannelNumber  LogicalChannelNumber,
              portNumber                   INTEGER(0..65535) OPTIONAL,
              multiplexParameters
                CHOICE {h222LogicalChannelParameters
                          H222LogicalChannelParameters,
                        -- H.223 parameters are never present in reverse direction
                        ...,
                        h2250LogicalChannelParameters
                          H2250LogicalChannelParameters} OPTIONAL, -- not present for H.223--
              ...,
              replacementFor               LogicalChannelNumber OPTIONAL
  } OPTIONAL, -- not present for unidirectional channel

  -- request
  ...,
  separateStack                     NetworkAccessParameters OPTIONAL,
  -- for Open requester to establish
  -- the stack
  forwardMultiplexAckParameters
    CHOICE {-- H.222 parameters are never present in the Ack
            -- H.223 parameters are never present in the Ack
            -- V.76 parameters are never present in the Ack
            h2250LogicalChannelAckParameters  H2250LogicalChannelAckParameters,
            ...} OPTIONAL,
  encryptionSync                    EncryptionSync OPTIONAL, -- used only by Master
  genericInformation                SEQUENCE OF GenericInformation OPTIONAL
}

-- generic information associated
-- with the message
OpenLogicalChannelReject ::= SEQUENCE {
  forwardLogicalChannelNumber  LogicalChannelNumber,
  cause
    CHOICE {unspecified                        NULL,
            unsuitableReverseParameters        NULL,
            dataTypeNotSupported               NULL,
            dataTypeNotAvailable               NULL,
            unknownDataType                    NULL,
            dataTypeALCombinationNotSupported  NULL,
            ...,
            multicastChannelNotAllowed         NULL,
            insufficientBandwidth              NULL,
            separateStackEstablishmentFailed   NULL,
            invalidSessionID                   NULL,
            masterSlaveConflict                NULL,
            waitForCommunicationMode           NULL,
            invalidDependentChannel            NULL,
            replacementForRejected             NULL,
            securityDenied                     NULL,
            qoSControlNotSupported             NULL}, --  added for callee to indicate

  -- that requested QoS support cannot be
  -- supported.
  ...,
  genericInformation           SEQUENCE OF GenericInformation OPTIONAL
}

-- generic information associated
-- with the message
OpenLogicalChannelConfirm ::= SEQUENCE {
  forwardLogicalChannelNumber  LogicalChannelNumber,
  ...,
  genericInformation           SEQUENCE OF GenericInformation OPTIONAL
}

-- generic information associated
-- with the message
H2250LogicalChannelAckParameters ::= SEQUENCE {
  nonStandard            SEQUENCE OF NonStandardParameter OPTIONAL,
  sessionID              INTEGER(1..255) OPTIONAL,
  mediaChannel           TransportAddressH245 OPTIONAL,
  mediaControlChannel    TransportAddressH245 OPTIONAL, -- forward RTCP

  -- channel
  dynamicRTPPayloadType  INTEGER(96..127) OPTIONAL, -- used only by

  -- the master or
  -- MC
  ...,
  flowControlToZero      BOOLEAN,
  portNumber             INTEGER(0..65535) OPTIONAL
}

CloseLogicalChannel ::= SEQUENCE {
  forwardLogicalChannelNumber  LogicalChannelNumber,
  source                       CHOICE {user  NULL,
                                       lcse  NULL},
  ...,
  reason
    CHOICE {unknown             NULL,
            reopen              NULL,
            reservationFailure  NULL,
            ...,
            networkErrorCode    INTEGER(0..255)}
} -- Indicates the error

-- code received from network
CloseLogicalChannelAck ::= SEQUENCE {
  forwardLogicalChannelNumber  LogicalChannelNumber,
  ...
}

RequestChannelClose ::= SEQUENCE {
  forwardLogicalChannelNumber  LogicalChannelNumber,
  ...,
  qosCapability                QOSCapability OPTIONAL,
  reason
    CHOICE {unknown             NULL,
            normal              NULL,
            reopen              NULL,
            reservationFailure  NULL,
            ...,
            networkErrorCode    INTEGER(0..255)}
} -- Indicates the error

-- code received from network
RequestChannelCloseAck ::= SEQUENCE {
  forwardLogicalChannelNumber  LogicalChannelNumber,
  ...
}

RequestChannelCloseReject ::= SEQUENCE {
  forwardLogicalChannelNumber  LogicalChannelNumber,
  cause                        CHOICE {unspecified  NULL,
                                       ...},
  ...
}

RequestChannelCloseRelease ::= SEQUENCE {
  forwardLogicalChannelNumber  LogicalChannelNumber,
  ...
}

-- =============================================================================
-- H.223 multiplex table definitions
-- =============================================================================
MultiplexEntrySend ::= SEQUENCE {
  sequenceNumber             SequenceNumber,
  multiplexEntryDescriptors  SET SIZE (1..15) OF MultiplexEntryDescriptor,
  ...
}

MultiplexEntryDescriptor ::= SEQUENCE {
  multiplexTableEntryNumber  MultiplexTableEntryNumber,
  elementList
    SEQUENCE SIZE (1..256) OF MultiplexElement OPTIONAL
}

MultiplexElement ::= SEQUENCE {
  type
    CHOICE {logicalChannelNumber  INTEGER(0..65535),
            subElementList        SEQUENCE SIZE (2..255) OF MultiplexElement
  },
  repeatCount
    CHOICE {finite            -- repeats of type -- INTEGER(1..65535),
            untilClosingFlag  -- used for last element -- NULL}
}

MultiplexTableEntryNumber ::= INTEGER(1..15)

MultiplexEntrySendAck ::= SEQUENCE {
  sequenceNumber             SequenceNumber,
  multiplexTableEntryNumber  SET SIZE (1..15) OF MultiplexTableEntryNumber,
  ...
}

MultiplexEntrySendReject ::= SEQUENCE {
  sequenceNumber         SequenceNumber,
  rejectionDescriptions
    SET SIZE (1..15) OF MultiplexEntryRejectionDescriptions,
  ...
}

MultiplexEntryRejectionDescriptions ::= SEQUENCE {
  multiplexTableEntryNumber  MultiplexTableEntryNumber,
  cause
    CHOICE {unspecifiedCause      NULL,
            descriptorTooComplex  NULL,
            ...},
  ...
}

MultiplexEntrySendRelease ::= SEQUENCE {
  multiplexTableEntryNumber  SET SIZE (1..15) OF MultiplexTableEntryNumber,
  ...
}

RequestMultiplexEntry ::= SEQUENCE {
  entryNumbers  SET SIZE (1..15) OF MultiplexTableEntryNumber,
  ...
}

RequestMultiplexEntryAck ::= SEQUENCE {
  entryNumbers  SET SIZE (1..15) OF MultiplexTableEntryNumber,
  ...
}

RequestMultiplexEntryReject ::= SEQUENCE {
  entryNumbers           SET SIZE (1..15) OF MultiplexTableEntryNumber,
  rejectionDescriptions
    SET SIZE (1..15) OF RequestMultiplexEntryRejectionDescriptions,
  ...
}

RequestMultiplexEntryRejectionDescriptions ::= SEQUENCE {
  multiplexTableEntryNumber  MultiplexTableEntryNumber,
  cause                      CHOICE {unspecifiedCause  NULL,
                                     ...},
  ...
}

RequestMultiplexEntryRelease ::= SEQUENCE {
  entryNumbers  SET SIZE (1..15) OF MultiplexTableEntryNumber,
  ...
}

-- =============================================================================
-- Request mode definitions
-- =============================================================================
-- RequestMode is a list, in order or preference, of modes that a terminal would
-- like to have transmitted to it.
RequestMode ::= SEQUENCE {
  sequenceNumber  SequenceNumber,
  requestedModes  SEQUENCE SIZE (1..256) OF ModeDescription,
  ...
}

RequestModeAck ::= SEQUENCE {
  sequenceNumber  SequenceNumber,
  response
    CHOICE {willTransmitMostPreferredMode  NULL,
            willTransmitLessPreferredMode  NULL,
            ...},
  ...
}

RequestModeReject ::= SEQUENCE {
  sequenceNumber  SequenceNumber,
  cause
    CHOICE {modeUnavailable       NULL,
            multipointConstraint  NULL,
            requestDenied         NULL,
            ...},
  ...
}

RequestModeRelease ::= SEQUENCE {...
}

-- =============================================================================
-- Request mode definitions: Mode description
-- =============================================================================
ModeDescription ::= SET SIZE (1..256) OF ModeElement

ModeElementType ::= CHOICE {
  nonStandard                NonStandardParameter,
  videoMode                  VideoMode,
  audioMode                  AudioMode,
  dataMode                   DataMode,
  encryptionMode             EncryptionMode,
  ...,
  h235Mode                   H235Mode,
  multiplexedStreamMode      MultiplexedStreamParameter,
  redundancyEncodingDTMode   RedundancyEncodingDTMode,
  multiplePayloadStreamMode  MultiplePayloadStreamMode,
  depFecMode                 DepFECMode, -- deprecated, do not use
  fecMode                    FECMode
}

ModeElement ::= SEQUENCE {
  type                                ModeElementType,
  h223ModeParameters                  H223ModeParameters OPTIONAL,
  ...,
  v76ModeParameters                   V76ModeParameters OPTIONAL,
  h2250ModeParameters                 H2250ModeParameters OPTIONAL,
  genericModeParameters               GenericCapability OPTIONAL,
  multiplexedStreamModeParameters     MultiplexedStreamModeParameters OPTIONAL,
  logicalChannelNumber                LogicalChannelNumber OPTIONAL
}

H235Mode ::= SEQUENCE {
  encryptionAuthenticationAndIntegrity  EncryptionAuthenticationAndIntegrity,
  mediaMode
    CHOICE {nonStandard  NonStandardParameter,
            videoMode    VideoMode,
            audioMode    AudioMode,
            dataMode     DataMode,
            ...},
  ...
}

MultiplexedStreamModeParameters ::= SEQUENCE {
  logicalChannelNumber  LogicalChannelNumber,
  ...
}

RedundancyEncodingDTMode ::= SEQUENCE {
  redundancyEncodingMethod  RedundancyEncodingMethod,
  primary                   RedundancyEncodingDTModeElement,
  secondary                 SEQUENCE OF RedundancyEncodingDTModeElement,
  ...
}

RedundancyEncodingDTModeElement ::= SEQUENCE {
  type
    CHOICE {nonStandard     NonStandardParameter,
            videoMode       VideoMode,
            audioMode       AudioMode,
            dataMode        DataMode,
            encryptionMode  EncryptionMode,
            h235Mode        H235Mode,
            ...,
            fecMode         FECMode},
  ...
}

MultiplePayloadStreamMode ::= SEQUENCE {
  elements  SEQUENCE OF MultiplePayloadStreamElementMode,
  ...
}

MultiplePayloadStreamElementMode ::= SEQUENCE {type  ModeElementType,
                                               ...
}

DepFECMode ::= CHOICE -- deprecated, do not use
                {
  rfc2733Mode
    SEQUENCE {mode
                CHOICE {redundancyEncoding  NULL,
                        separateStream
                          CHOICE {differentPort
                                    SEQUENCE {protectedSessionID
                                                INTEGER(1..255),
                                              protectedPayloadType
                                                INTEGER(0..127) OPTIONAL,
                                              ...},
                                  samePort
                                    SEQUENCE {protectedType  ModeElementType,
                                              ...},
                                  ...},
                        ...},
              ...},
  ...
}

FECMode ::= SEQUENCE {
  protectedElement  ModeElementType,
  fecScheme         -- identifies encoding scheme -- OBJECT IDENTIFIER OPTIONAL,
  rfc2733Format
    CHOICE {rfc2733rfc2198   -- RFC 2198 redundancy -- MaxRedundancy,
            rfc2733sameport  -- separate packet, same port -- MaxRedundancy,
            rfc2733diffport  -- separate packet and port -- MaxRedundancy
  } OPTIONAL,
  ...
}

H223ModeParameters ::= SEQUENCE {
  adaptationLayerType
    CHOICE {nonStandard                NonStandardParameter,
            al1Framed                  NULL,
            al1NotFramed               NULL,
            al2WithoutSequenceNumbers  NULL,
            al2WithSequenceNumbers     NULL,
            al3
              SEQUENCE {controlFieldOctets  INTEGER(0..2),
                        sendBufferSize
                          -- units octets -- INTEGER(0..16777215)},
            ...,
            al1M                       H223AL1MParameters,
            al2M                       H223AL2MParameters,
            al3M                       H223AL3MParameters},
  segmentableFlag      BOOLEAN,
  ...
}

V76ModeParameters ::= CHOICE {
  suspendResumewAddress   NULL,
  suspendResumewoAddress  NULL,
  ...
}

H2250ModeParameters ::= SEQUENCE {
  redundancyEncodingMode  RedundancyEncodingMode OPTIONAL,
  ...
}

RedundancyEncodingMode ::= SEQUENCE {
  redundancyEncodingMethod  RedundancyEncodingMethod,
  secondaryEncoding
    CHOICE {nonStandard  NonStandardParameter,
            audioData    AudioMode,
            ...} OPTIONAL,
  ...
}

-- =============================================================================
-- Request mode definitions: Video modes
-- =============================================================================
VideoMode ::= CHOICE {
  nonStandard       NonStandardParameter,
  h261VideoMode     H261VideoMode,
  h262VideoMode     H262VideoMode,
  h263VideoMode     H263VideoMode,
  is11172VideoMode  IS11172VideoMode,
  ...,
  genericVideoMode  GenericCapability
}

H261VideoMode ::= SEQUENCE {
  resolution              CHOICE {qcif  NULL,
                                  cif   NULL},
  bitRate                 INTEGER(1..19200), -- units 100 bit/s
  stillImageTransmission  BOOLEAN,
  ...
}

H262VideoMode ::= SEQUENCE {
  profileAndLevel
    CHOICE {profileAndLevel-SPatML         NULL,
            profileAndLevel-MPatLL         NULL,
            profileAndLevel-MPatML         NULL,
            profileAndLevel-MPatH-14       NULL,
            profileAndLevel-MPatHL         NULL,
            profileAndLevel-SNRatLL        NULL,
            profileAndLevel-SNRatML        NULL,
            profileAndLevel-SpatialatH-14  NULL,
            profileAndLevel-HPatML         NULL,
            profileAndLevel-HPatH-14       NULL,
            profileAndLevel-HPatHL         NULL,
            ...},
  videoBitRate         INTEGER(0..1073741823) OPTIONAL, -- units 400 bit/s
  vbvBufferSize        INTEGER(0..262143) OPTIONAL, -- units 16 384 bits
  samplesPerLine       INTEGER(0..16383) OPTIONAL, -- units samples/line
  linesPerFrame        INTEGER(0..16383) OPTIONAL, -- units lines/frame
  framesPerSecond      INTEGER(0..15) OPTIONAL, -- frame_rate_code
  luminanceSampleRate  INTEGER(0..4294967295) OPTIONAL, -- units samples/s
  ...
}

H263VideoMode ::= SEQUENCE {
  resolution
    CHOICE {sqcif   NULL,
            qcif    NULL,
            cif     NULL,
            cif4    NULL,
            cif16   NULL,
            ...,
            custom  NULL},
  bitRate                  INTEGER(1..19200), -- units 100 bit/s
  unrestrictedVector       BOOLEAN,
  arithmeticCoding         BOOLEAN,
  advancedPrediction       BOOLEAN,
  pbFrames                 BOOLEAN,
  ...,
  errorCompensation        BOOLEAN,
  enhancementLayerInfo     EnhancementLayerInfo OPTIONAL,
  h263Options              H263Options OPTIONAL
}

IS11172VideoMode ::= SEQUENCE {
  constrainedBitstream  BOOLEAN,
  videoBitRate          INTEGER(0..1073741823) OPTIONAL, -- units

  -- 400 bit/s
  vbvBufferSize         INTEGER(0..262143) OPTIONAL, -- units

  -- 16 384 bits
  samplesPerLine        INTEGER(0..16383) OPTIONAL, -- units

  -- samples/line
  linesPerFrame         INTEGER(0..16383) OPTIONAL, -- units

  -- lines/frame
  pictureRate           INTEGER(0..15) OPTIONAL,
  luminanceSampleRate   INTEGER(0..4294967295) OPTIONAL, -- units

  -- samples/s
  ...
}

-- =============================================================================
-- Request mode definitions: Audio modes
-- =============================================================================
AudioMode ::= CHOICE {
  nonStandard          NonStandardParameter,
  g711Alaw64k          NULL,
  g711Alaw56k          NULL,
  g711Ulaw64k          NULL,
  g711Ulaw56k          NULL,
  g722-64k             NULL,
  g722-56k             NULL,
  g722-48k             NULL,
  g728                 NULL,
  g729                 NULL,
  g729AnnexA           NULL,
  g7231
    CHOICE {noSilenceSuppressionLowRate   NULL,
            noSilenceSuppressionHighRate  NULL,
            silenceSuppressionLowRate     NULL,
            silenceSuppressionHighRate    NULL},
  is11172AudioMode     IS11172AudioMode,
  is13818AudioMode     IS13818AudioMode,
  ...,
  g729wAnnexB          INTEGER(1..256),
  g729AnnexAwAnnexB    INTEGER(1..256),
  g7231AnnexCMode      G7231AnnexCMode,
  gsmFullRate          GSMAudioCapability,
  gsmHalfRate          GSMAudioCapability,
  gsmEnhancedFullRate  GSMAudioCapability,
  genericAudioMode     GenericCapability,
  g729Extensions       G729Extensions,
  vbd                  VBDMode
}

IS11172AudioMode ::= SEQUENCE {
  audioLayer
    CHOICE {audioLayer1  NULL,
            audioLayer2  NULL,
            audioLayer3  NULL},
  audioSampling
    CHOICE {audioSampling32k   NULL,
            audioSampling44k1  NULL,
            audioSampling48k   NULL},
  multichannelType
    CHOICE {singleChannel     NULL,
            twoChannelStereo  NULL,
            twoChannelDual    NULL},
  bitRate           INTEGER(1..448), -- units kbit/s
  ...
}

IS13818AudioMode ::= SEQUENCE {
  audioLayer
    CHOICE {audioLayer1  NULL,
            audioLayer2  NULL,
            audioLayer3  NULL},
  audioSampling
    CHOICE {audioSampling16k    NULL,
            audioSampling22k05  NULL,
            audioSampling24k    NULL,
            audioSampling32k    NULL,
            audioSampling44k1   NULL,
            audioSampling48k    NULL},
  multichannelType
    CHOICE {singleChannel        NULL,
            twoChannelStereo     NULL,
            twoChannelDual       NULL,
            threeChannels2-1     NULL,
            threeChannels3-0     NULL,
            fourChannels2-0-2-0  NULL,
            fourChannels2-2      NULL,
            fourChannels3-1      NULL,
            fiveChannels3-0-2-0  NULL,
            fiveChannels3-2      NULL},
  lowFrequencyEnhancement  BOOLEAN,
  multilingual             BOOLEAN,
  bitRate                  INTEGER(1..1130), -- units kbit/s
  ...
}

G7231AnnexCMode ::= SEQUENCE {
  maxAl-sduAudioFrames  INTEGER(1..256),
  silenceSuppression    BOOLEAN,
  g723AnnexCAudioMode
    SEQUENCE {highRateMode0  INTEGER(27..78), -- units octets--
              highRateMode1  INTEGER(27..78), -- units octets--
              lowRateMode0   INTEGER(23..66), -- units octets--
              lowRateMode1   INTEGER(23..66), -- units octets--
              sidMode0       INTEGER(6..17), -- units octets--
              sidMode1       INTEGER(6..17), -- units octets--
              ...},
  ...
}

VBDMode ::= SEQUENCE {type  AudioMode, -- shall not be "vbd"
                      ...
}

-- =============================================================================
-- Request mode definitions: Data modes
-- =============================================================================
DataMode ::= SEQUENCE {
  application
    CHOICE {nonStandard           NonStandardParameter,
            t120                  DataProtocolCapability,
            dsm-cc                DataProtocolCapability,
            userData              DataProtocolCapability,
            t84                   DataProtocolCapability,
            t434                  DataProtocolCapability,
            h224                  DataProtocolCapability,
            nlpid
              SEQUENCE {nlpidProtocol  DataProtocolCapability,
                        nlpidData      OCTET STRING},
            dsvdControl           NULL,
            h222DataPartitioning  DataProtocolCapability,
            ...,
            t30fax                DataProtocolCapability,
            t140                  DataProtocolCapability,
            t38fax
              SEQUENCE {t38FaxProtocol  DataProtocolCapability,
                        t38FaxProfile   T38FaxProfile},
            genericDataMode       GenericCapability},
  bitRate      INTEGER(0..4294967295), -- units 100 bit/s
  ...
}

-- =============================================================================
-- Request mode definitions: Encryption modes
-- =============================================================================
EncryptionMode ::= CHOICE {
  nonStandard     NonStandardParameter,
  h233Encryption  NULL,
  ...
}

-- =============================================================================
-- Round Trip Delay definitions
-- =============================================================================
RoundTripDelayRequest ::= SEQUENCE {sequenceNumber  SequenceNumber,
                                    ...
}

RoundTripDelayResponse ::= SEQUENCE {sequenceNumber  SequenceNumber,
                                     ...
}

-- =============================================================================
-- Maintenance Loop definitions
-- =============================================================================
MaintenanceLoopRequest ::= SEQUENCE {
  type
    CHOICE {systemLoop          NULL,
            mediaLoop           LogicalChannelNumber,
            logicalChannelLoop  LogicalChannelNumber,
            ...},
  ...
}

MaintenanceLoopAck ::= SEQUENCE {
  type
    CHOICE {systemLoop          NULL,
            mediaLoop           LogicalChannelNumber,
            logicalChannelLoop  LogicalChannelNumber,
            ...},
  ...
}

MaintenanceLoopReject ::= SEQUENCE {
  type
    CHOICE {systemLoop          NULL,
            mediaLoop           LogicalChannelNumber,
            logicalChannelLoop  LogicalChannelNumber,
            ...},
  cause  CHOICE {canNotPerformLoop  NULL,
                 ...},
  ...
}

MaintenanceLoopOffCommand ::= SEQUENCE {...
}

-- =============================================================================
-- Communication Mode definitions
-- =============================================================================
CommunicationModeCommand ::= SEQUENCE {
  communicationModeTable  SET SIZE (1..256) OF CommunicationModeTableEntry,
  ...
}

CommunicationModeRequest ::= SEQUENCE {...
}

CommunicationModeResponse ::= CHOICE {
  communicationModeTable  SET SIZE (1..256) OF CommunicationModeTableEntry,
  ...
}

CommunicationModeTableEntry ::= SEQUENCE {
  nonStandard                     SEQUENCE OF NonStandardParameter OPTIONAL,
  sessionID                       INTEGER(1..255),
  associatedSessionID             INTEGER(1..255) OPTIONAL,
  terminalLabel                   TerminalLabel OPTIONAL, -- if not present,

  -- it refers to
  -- all
  -- participants in
  -- the conference
  sessionDescription              BMPString(SIZE (1..128)),
  -- Basic ISO/IEC 10646 (Unicode)
  dataType
    CHOICE {videoData  VideoCapability,
            audioData  AudioCapability,
            data       DataApplicationCapability,
            ...},
  mediaChannel                    TransportAddressH245 OPTIONAL,
  mediaGuaranteedDelivery         BOOLEAN OPTIONAL,
  mediaControlChannel             TransportAddressH245 OPTIONAL,
  -- reverse RTCP channel
  mediaControlGuaranteedDelivery  BOOLEAN OPTIONAL,
  ...,
  redundancyEncoding              RedundancyEncoding OPTIONAL,
  sessionDependency               INTEGER(1..255) OPTIONAL,
  destination                     TerminalLabel OPTIONAL
}

-- =============================================================================
-- Conference Request definitions
-- =============================================================================
ConferenceRequest ::= CHOICE {
  terminalListRequest         NULL, -- same as H.230 TCU (term->MC)
  makeMeChair                 NULL, -- same as H.230 CCA (term->MC)
  cancelMakeMeChair           NULL, -- same as H.230 CIS (term->MC)
  dropTerminal                TerminalLabel, -- same as H.230 CCD(term->MC)
  requestTerminalID           TerminalLabel, -- same as TCP (term->MC)
  enterH243Password           NULL, -- same as H.230 TCS1(MC->term)
  enterH243TerminalID         NULL, -- same as H.230 TCS2/TCI

  --  (MC->term)
  enterH243ConferenceID       NULL, -- same as H.230 TCS3 (MC->term)
  ...,
  enterExtensionAddress       NULL, -- same as H.230 TCS4 (GW->term)
  requestChairTokenOwner      NULL, -- same as H.230 TCA (term->MC)
  requestTerminalCertificate
    SEQUENCE {terminalLabel          TerminalLabel OPTIONAL,
              certSelectionCriteria  CertSelectionCriteria OPTIONAL,
              sRandom                INTEGER(1..4294967295) OPTIONAL,
              -- this is the requester's challenge
              ...},
  broadcastMyLogicalChannel   LogicalChannelNumber, -- similar to H.230 MCV
  makeTerminalBroadcaster     TerminalLabel, -- similar to H.230 VCB
  sendThisSource              TerminalLabel, -- similar to H.230 VCS
  requestAllTerminalIDs       NULL,
  remoteMCRequest             RemoteMCRequest
}

CertSelectionCriteria ::= SEQUENCE SIZE (1..16) OF Criteria

Criteria ::= SEQUENCE {
  field  OBJECT IDENTIFIER, -- may include

  -- certificate type
  value  OCTET STRING(SIZE (1..65535)),
  ...
}

TerminalLabel ::= SEQUENCE {
  mcuNumber       McuNumber,
  terminalNumber  TerminalNumber,
  ...
}

McuNumber ::= INTEGER(0..192)

TerminalNumber ::= INTEGER(0..192)

-- =============================================================================
-- Conference Response definitions
-- =============================================================================
ConferenceResponse ::= CHOICE {
  mCTerminalIDResponse
    SEQUENCE-- response to TCP --
     -- (same as TIP)
    {-- sent by MC only--terminalLabel  TerminalLabel,
                         terminalID     TerminalID,
                         ...},
  terminalIDResponse
    SEQUENCE-- response to TCS2 or TCI-- {-- same as IIS--terminalLabel
                                                            TerminalLabel, -- (term->MC)--
                                                          terminalID
                                                            TerminalID,
                                                          ...},
  conferenceIDResponse
    SEQUENCE-- response to TCS3-- {-- same as IIS--terminalLabel  TerminalLabel, -- (term->MC)--
                                                   conferenceID   ConferenceID,
                                                   ...},
  passwordResponse
    SEQUENCE-- response to TCS1-- {-- same as IIS--terminalLabel  TerminalLabel, -- (term->MC)--
                                                   password       Password,
                                                   ...},
  terminalListResponse               SET SIZE (1..256) OF TerminalLabel,
  videoCommandReject                 NULL, -- same as H.230 VCR
  terminalDropReject                 NULL, -- same as H.230 CIR
  makeMeChairResponse
    CHOICE-- same as H.230 CCR-- {grantedChairToken  NULL, -- same as H.230 CIT--
                                  deniedChairToken   NULL, -- same as H.230 CCR--
                                  ...},
  ...,
  extensionAddressResponse
    SEQUENCE-- response to TCS4-- {extensionAddress  TerminalID, -- same as IIS (term->GW)--
                                   ...},
  chairTokenOwnerResponse
    SEQUENCE-- response to TCA (same as TIR) --
     -- sent by MC only
    {terminalLabel  TerminalLabel,
     terminalID     TerminalID,
     ...},
  terminalCertificateResponse
    SEQUENCE {terminalLabel        TerminalLabel OPTIONAL,
              certificateResponse  OCTET STRING(SIZE (1..65535)) OPTIONAL,
              ...},
  broadcastMyLogicalChannelResponse
    CHOICE {grantedBroadcastMyLogicalChannel  NULL, -- similar to H.230 MVA--
            deniedBroadcastMyLogicalChannel   NULL, -- similar to H.230 MVR--
            ...},
  makeTerminalBroadcasterResponse
    CHOICE {grantedMakeTerminalBroadcaster  NULL,
            deniedMakeTerminalBroadcaster   NULL,
            ...},
  sendThisSourceResponse
    CHOICE {grantedSendThisSource  NULL,
            deniedSendThisSource   NULL,
            ...},
  requestAllTerminalIDsResponse      RequestAllTerminalIDsResponse,
  remoteMCResponse                   RemoteMCResponse
}

TerminalID ::= OCTET STRING(SIZE (1..128)) -- as per H.230


ConferenceID ::= OCTET STRING(SIZE (1..32))

Password ::= OCTET STRING(SIZE (1..32))

RequestAllTerminalIDsResponse ::= SEQUENCE {
  terminalInformation  SEQUENCE OF TerminalInformation,
  ...
}

TerminalInformation ::= SEQUENCE {
  terminalLabel  TerminalLabel,
  terminalID     TerminalID,
  ...
}

-- =============================================================================
-- Remote MC Request definitions
-- =============================================================================
RemoteMCRequest ::= CHOICE {
  masterActivate  NULL,
  slaveActivate   NULL,
  deActivate      NULL,
  ...
}

RemoteMCResponse ::= CHOICE {
  accept  NULL,
  reject  CHOICE {unspecified           NULL,
                  functionNotSupported  NULL,
                  ...},
  ...
}

-- =============================================================================
-- Multilink definitions
-- =============================================================================
MultilinkRequest ::= CHOICE {
  nonStandard            NonStandardMessageH245,
  callInformation
    SEQUENCE {maxNumberOfAdditionalConnections  INTEGER(1..65535),
              ...},
  addConnection
    SEQUENCE {sequenceNumber      SequenceNumber, -- Unique ID of request--
              dialingInformation  DialingInformation,
              ...},
  removeConnection
    SEQUENCE {connectionIdentifier  ConnectionIdentifier,
              ...},
  maximumHeaderInterval
    SEQUENCE {requestType
                CHOICE {currentIntervalInformation  NULL,
                        requestedInterval           INTEGER(0..65535), -- Max Header --
                        -- Interval,
                        -- milliseconds
                        ...},
              ...},
  ...
}

MultilinkResponse ::= CHOICE {
  nonStandard            NonStandardMessageH245,
  callInformation
    SEQUENCE {dialingInformation     DialingInformation,
              callAssociationNumber  INTEGER(0..4294967295),
              ...},
  addConnection
    SEQUENCE {sequenceNumber  SequenceNumber, -- Equal to value in request--
              responseCode
                CHOICE {accepted  NULL,
                        rejected
                          CHOICE {connectionsNotAvailable  NULL, -- due to any technical reason--
                                  userRejected             NULL,
                                  ...},
                        ...},
              ...},
  removeConnection
    SEQUENCE {connectionIdentifier  ConnectionIdentifier,
              ...},
  maximumHeaderInterval
    SEQUENCE {currentInterval  INTEGER(0..65535), -- Max Header --
              -- Interval,
              -- milliseconds
              ...},
  ...
}

MultilinkIndication ::= CHOICE {
  nonStandard     NonStandardMessageH245,
  crcDesired      SEQUENCE {...},
  excessiveError  SEQUENCE {connectionIdentifier  ConnectionIdentifier,
                            ...},
  ...
}

DialingInformation ::= CHOICE {
  nonStandard       NonStandardMessageH245,
  differential      SET SIZE (1..65535) OF DialingInformationNumber,
  -- list of numbers for all additional
  -- channels; only least significant digits
  -- different from initial channel's number
  infoNotAvailable  INTEGER(1..65535), -- maximum No. of

  -- additional channels
  ...
}

DialingInformationNumber ::= SEQUENCE {
  networkAddress  NumericString(SIZE (0..40)),
  subAddress      IA5String(SIZE (1..40)) OPTIONAL,
  networkType     SET SIZE (1..255) OF DialingInformationNetworkType,
  ...
}

DialingInformationNetworkType ::= CHOICE {
  nonStandard  NonStandardMessageH245,
  n-isdn       NULL,
  gstn         NULL,
  ...,
  mobile       NULL
}

ConnectionIdentifier ::= SEQUENCE {
  channelTag      INTEGER(0..4294967295), -- from H.226
  sequenceNumber  INTEGER(0..4294967295), -- from H.226
  ...
}

-- =============================================================================
-- Logical channel bit-rate change definitions
-- =============================================================================
MaximumBitRate ::= INTEGER(0..4294967295) -- units of 100 bit/s


LogicalChannelRateRequest ::= SEQUENCE {
  sequenceNumber        SequenceNumber,
  logicalChannelNumber  LogicalChannelNumber,
  maximumBitRate        MaximumBitRate,
  ...
}

LogicalChannelRateAcknowledge ::= SEQUENCE {
  sequenceNumber        SequenceNumber,
  logicalChannelNumber  LogicalChannelNumber,
  maximumBitRate        MaximumBitRate,
  ...
}

LogicalChannelRateReject ::= SEQUENCE {
  sequenceNumber         SequenceNumber,
  logicalChannelNumber   LogicalChannelNumber,
  rejectReason           LogicalChannelRateRejectReason,
  currentMaximumBitRate  MaximumBitRate OPTIONAL,
  ...
}

LogicalChannelRateRejectReason ::= CHOICE {
  undefinedReason        NULL,
  insufficientResources  NULL,
  ...
}

LogicalChannelRateRelease ::= SEQUENCE {...
}

-- =============================================================================
-- Command Message definitions
-- =============================================================================
-- =============================================================================
-- Command Message: Send Terminal Capability Set
-- =============================================================================
SendTerminalCapabilitySet ::= CHOICE {
  specificRequest
    SEQUENCE {multiplexCapability          BOOLEAN,
              capabilityTableEntryNumbers
                SET SIZE (1..65535) OF CapabilityTableEntryNumber OPTIONAL,
              capabilityDescriptorNumbers
                SET SIZE (1..256) OF CapabilityDescriptorNumber OPTIONAL,
              ...},
  genericRequest   NULL,
  ...
}

-- =============================================================================
-- Command Message: Encryption
-- =============================================================================
EncryptionCommand ::= CHOICE {
  encryptionSE           OCTET STRING, -- per H.233, but no

  -- error protection
  encryptionIVRequest    NULL, -- requests new IV
  encryptionAlgorithmID
    SEQUENCE {h233AlgorithmIdentifier  SequenceNumber,
              associatedAlgorithm      NonStandardParameter},
  ...
}

-- =============================================================================
-- Command Message: Flow Control
-- =============================================================================
FlowControlCommand ::= SEQUENCE {
  scope
    CHOICE {logicalChannelNumber  LogicalChannelNumber,
            resourceID            INTEGER(0..65535),
            wholeMultiplex        NULL},
  restriction
    CHOICE {maximumBitRate  INTEGER(0..16777215), -- units 100 bit/s --
            noRestriction   NULL},
  ...
}

-- =============================================================================
-- Command Message: Change or End Session
-- =============================================================================
EndSessionCommand ::= CHOICE {
  nonStandard         NonStandardParameter,
  disconnect          NULL,
  gstnOptions
    CHOICE {telephonyMode  NULL,
            v8bis          NULL,
            v34DSVD        NULL,
            v34DuplexFAX   NULL,
            v34H324        NULL,
            ...},
  ...,
  isdnOptions
    CHOICE {telephonyMode   NULL,
            v140            NULL,
            terminalOnHold  NULL,
            ...},
  genericInformation  SEQUENCE OF GenericInformation
}

-- generic information associated
-- with the message
-- =============================================================================
-- Command Message: Conference Commands
-- =============================================================================
ConferenceCommand ::= CHOICE {
  broadcastMyLogicalChannel        LogicalChannelNumber, -- similar to H.230 MCV
  cancelBroadcastMyLogicalChannel  LogicalChannelNumber, -- similar to

  -- H.230 Cancel-MCV
  makeTerminalBroadcaster          TerminalLabel, -- same as H.230 VCB
  cancelMakeTerminalBroadcaster    NULL, -- same as H.230

  -- Cancel-VCB
  sendThisSource                   TerminalLabel, -- same as H.230 VCS
  cancelSendThisSource             NULL, -- same as H.230

  -- cancel VCS
  dropConference                   NULL, -- same as H.230 CCK
  ...,
  substituteConferenceIDCommand    SubstituteConferenceIDCommand
}

SubstituteConferenceIDCommand ::= SEQUENCE {
  conferenceIdentifier  OCTET STRING(SIZE (16)),
  ...
}

-- =============================================================================
-- Command Message: Miscellaneous H.230-like commands
-- =============================================================================
EncryptionUpdateDirection ::= CHOICE {
  masterToSlave  NULL,
  slaveToMaster  NULL,
  ...
}

MiscellaneousCommand ::= SEQUENCE {
  logicalChannelNumber  LogicalChannelNumber,
  type
    CHOICE {equaliseDelay                         NULL, -- same as H.230 ACE--
            zeroDelay                             NULL, -- same as H.230 ACZ--
            multipointModeCommand                 NULL,
            cancelMultipointModeCommand           NULL,
            videoFreezePicture                    NULL,
            videoFastUpdatePicture                NULL,
            videoFastUpdateGOB
              SEQUENCE {firstGOB      INTEGER(0..17),
                        numberOfGOBs  INTEGER(1..18)},
            videoTemporalSpatialTradeOff          INTEGER(0..31), -- commands a trade-off value--
            videoSendSyncEveryGOB                 NULL,
            videoSendSyncEveryGOBCancel           NULL,
            ...,
            videoFastUpdateMB
              SEQUENCE {firstGOB     INTEGER(0..255) OPTIONAL,
                        firstMB      INTEGER(1..8192) OPTIONAL,
                        numberOfMBs  INTEGER(1..8192),
                        ...},
            maxH223MUXPDUsize                     INTEGER(1..65535), -- units octets--
            encryptionUpdate                      EncryptionSync,
            encryptionUpdateRequest               EncryptionUpdateRequest,
            switchReceiveMediaOff                 NULL,
            switchReceiveMediaOn                  NULL,
            progressiveRefinementStart
              SEQUENCE {repeatCount
                          CHOICE {doOneProgression                     NULL,
                                  doContinuousProgressions             NULL,
                                  doOneIndependentProgression          NULL,
                                  doContinuousIndependentProgressions  NULL,
                                  ...},
                        ...},
            progressiveRefinementAbortOne         NULL,
            progressiveRefinementAbortContinuous  NULL,
            videoBadMBs
              SEQUENCE {firstMB            INTEGER(1..9216),
                        numberOfMBs        INTEGER(1..9216),
                        temporalReference  INTEGER(0..1023),
                        ...},
            lostPicture                           SEQUENCE OF PictureReference,
            lostPartialPicture
              SEQUENCE {pictureReference  PictureReference,
                        firstMB           INTEGER(1..9216),
                        numberOfMBs       INTEGER(1..9216),
                        ...},
            recoveryReferencePicture              SEQUENCE OF PictureReference,
            encryptionUpdateCommand
              SEQUENCE-- for ack'ed key update in H.235V3-- {encryptionSync

                                                               EncryptionSync,
                                                             multiplePayloadStream

                                                               MultiplePayloadStream
                                                                 OPTIONAL,
                                                             ...},
            encryptionUpdateAck
              SEQUENCE {synchFlag  INTEGER(0..255),
                        ...}},
  ...,
  direction             EncryptionUpdateDirection OPTIONAL
}

KeyProtectionMethod ::=
  SEQUENCE -- specify how the new

   -- key is to be protected
  {
  secureChannel     BOOLEAN,
  sharedSecret      BOOLEAN,
  certProtectedKey  BOOLEAN,
  ...
}

EncryptionUpdateRequest ::= SEQUENCE {
  keyProtectionMethod  KeyProtectionMethod OPTIONAL,
  ...,
  synchFlag            INTEGER(0..255) OPTIONAL
}

PictureReference ::= CHOICE {
  pictureNumber         INTEGER(0..1023),
  longTermPictureIndex  INTEGER(0..255),
  ...
}

-- =============================================================================
-- Command Message: H.223 Multiplex Reconfiguration
-- =============================================================================
H223MultiplexReconfiguration ::= CHOICE {
  h223ModeChange
    CHOICE {toLevel0                    NULL,
            toLevel1                    NULL,
            toLevel2                    NULL,
            toLevel2withOptionalHeader  NULL,
            ...},
  h223AnnexADoubleFlag  CHOICE {start  NULL,
                                stop   NULL,
                                ...},
  ...
}

-- =============================================================================
-- Command Message: New ATM virtual channel command
-- =============================================================================
NewATMVCCommand ::= SEQUENCE {
  resourceID                   INTEGER(0..65535),
  bitRate                      INTEGER(1..65535), -- units 64 kbit/s
  bitRateLockedToPCRClock      BOOLEAN,
  bitRateLockedToNetworkClock  BOOLEAN,
  aal
    CHOICE {aal1
              SEQUENCE {clockRecovery
                          CHOICE {nullClockRecovery      NULL,
                                  srtsClockRecovery      NULL,
                                  adaptiveClockRecovery  NULL,
                                  ...},
                        errorCorrection
                          CHOICE {nullErrorCorrection  NULL,
                                  longInterleaver      NULL,
                                  shortInterleaver     NULL,
                                  errorCorrectionOnly  NULL,
                                  ...},
                        structuredDataTransfer  BOOLEAN,
                        partiallyFilledCells    BOOLEAN,
                        ...},
            aal5
              SEQUENCE {forwardMaximumSDUSize   INTEGER(0..65535), -- units octets--
                        backwardMaximumSDUSize  INTEGER(0..65535), -- units octets--
                        ...},
            ...},
  multiplex
    CHOICE {noMultiplex      NULL,
            transportStream  NULL,
            programStream    NULL,
            ...},
  reverseParameters
    SEQUENCE {bitRate                      INTEGER(1..65535), -- units 64 kbit/s--
              bitRateLockedToPCRClock      BOOLEAN,
              bitRateLockedToNetworkClock  BOOLEAN,
              multiplex
                CHOICE {noMultiplex      NULL,
                        transportStream  NULL,
                        programStream    NULL,
                        ...},
              ...},
  ...
}

-- =============================================================================
-- Command Message: Mobile Multilink Reconfiguration command
-- =============================================================================
MobileMultilinkReconfigurationCommand ::= SEQUENCE {
  sampleSize       INTEGER(1..255),
  samplesPerFrame  INTEGER(1..255),
  status           CHOICE {synchronized     NULL,
                           reconfiguration  NULL,
                           ...},
  ...
}

-- =============================================================================
-- Indication Message definitions
-- =============================================================================
-- =============================================================================
-- Indication Message: Function not understood
-- =============================================================================
-- This is used to return a request, response or command that is not understood
FunctionNotUnderstood ::= CHOICE {
  request   RequestMessage,
  response  ResponseMessage,
  command   CommandMessage
}

-- =============================================================================
-- Indication Message: Function not Supported
-- =============================================================================
-- This is used to return a complete request, response or command that is not
-- recognized
FunctionNotSupported ::= SEQUENCE {
  cause
    CHOICE {syntaxError      NULL,
            semanticError    NULL,
            unknownFunction  NULL,
            ...},
  returnedFunction  OCTET STRING OPTIONAL,
  ...
}

-- =============================================================================
-- Indication Message: Conference
-- =============================================================================
ConferenceIndication ::= CHOICE {
  sbeNumber                               INTEGER(0..9), -- same as H.230 SBE Number
  terminalNumberAssign                    TerminalLabel, -- same as H.230 TIA
  terminalJoinedConference                TerminalLabel, -- same as H.230 TIN
  terminalLeftConference                  TerminalLabel, -- same as H.230 TID
  seenByAtLeastOneOther                   NULL, -- same as H.230 MIV
  cancelSeenByAtLeastOneOther             NULL, -- same as H.230 cancel MIV
  seenByAll                               NULL, -- like H.230 MIV
  cancelSeenByAll                         NULL, -- like H.230 MIV
  terminalYouAreSeeing                    TerminalLabel, -- same as H.230 VIN
  requestForFloor                         NULL, -- same as H.230 TIF
  ...,
  withdrawChairToken                      NULL, -- same as H.230 CCR MC-> chair
  floorRequested                          TerminalLabel, -- same as H.230 TIF MC-> chair
  terminalYouAreSeeingInSubPictureNumber
    TerminalYouAreSeeingInSubPictureNumber,
  videoIndicateCompose                    VideoIndicateCompose,
  masterMCU                               NULL, -- same as H.230 MIM
  cancelMasterMCU                         -- same as H.230 cancel MIM -- NULL
}

TerminalYouAreSeeingInSubPictureNumber ::= SEQUENCE {
  terminalNumber    TerminalNumber,
  subPictureNumber  INTEGER(0..255),
  ...,
  mcuNumber         McuNumber
}

VideoIndicateCompose ::= SEQUENCE {compositionNumber  INTEGER(0..255),
                                   ...
}

-- =============================================================================
-- Indication Message: Miscellaneous H.230-like indication
-- =============================================================================
MiscellaneousIndication ::= SEQUENCE {
  logicalChannelNumber  LogicalChannelNumber,
  type
    CHOICE {logicalChannelActive             NULL, -- same as H.230 AIA and VIA--
            logicalChannelInactive           NULL, -- same as H.230 AIM and VIS--
            multipointConference             NULL,
            cancelMultipointConference       NULL,
            multipointZeroComm               NULL, -- same as H.230 MIZ--
            cancelMultipointZeroComm         NULL, -- same as H.230 cancel MIZ--
            multipointSecondaryStatus        NULL, -- same as H.230 MIS--
            cancelMultipointSecondaryStatus  NULL, -- same as H.230 cancel MIS--
            videoIndicateReadyToActivate     NULL, -- same as H.230 VIR--
            videoTemporalSpatialTradeOff     INTEGER(0..31), -- indicates current --
            -- trade-off
            ...,
            videoNotDecodedMBs
              SEQUENCE {firstMB            INTEGER(1..8192),
                        numberOfMBs        INTEGER(1..8192),
                        temporalReference  INTEGER(0..255),
                        ...},
            transportCapability              TransportCapability},
  ...
}

-- =============================================================================
-- Indication Message: Jitter Indication
-- =============================================================================
JitterIndication ::= SEQUENCE {
  scope
    CHOICE {logicalChannelNumber  LogicalChannelNumber,
            resourceID            INTEGER(0..65535),
            wholeMultiplex        NULL},
  estimatedReceivedJitterMantissa  INTEGER(0..3),
  estimatedReceivedJitterExponent  INTEGER(0..7),
  skippedFrameCount                INTEGER(0..15) OPTIONAL,
  additionalDecoderBuffer          INTEGER(0..262143) OPTIONAL,
  -- 262143 is 2^18 - 1
  ...
}

-- =============================================================================
-- Indication Message: H.223 logical channel skew
-- =============================================================================
H223SkewIndication ::= SEQUENCE {
  logicalChannelNumber1  LogicalChannelNumber,
  logicalChannelNumber2  LogicalChannelNumber,
  skew                   INTEGER(0..4095), -- units milliseconds
  ...
}

-- =============================================================================
-- Indication Message: H.225.0 maximum logical channel skew
-- =============================================================================
H2250MaximumSkewIndication ::= SEQUENCE {
  logicalChannelNumber1  LogicalChannelNumber,
  logicalChannelNumber2  LogicalChannelNumber,
  maximumSkew            INTEGER(0..4095), -- units milliseconds
  ...
}

-- =============================================================================
-- Indication Message: MC Location Indication
-- =============================================================================
MCLocationIndication ::= SEQUENCE {
  signalAddress  TransportAddressH245, -- this is the

  -- H.323 Call Signalling
  -- address of the entity
  -- which contains the MC
  ...
}

-- =============================================================================
-- Indication Message: Vendor Identification
-- =============================================================================
VendorIdentification ::= SEQUENCE {
  vendor         NonStandardIdentifier,
  productNumber  OCTET STRING(SIZE (1..256)) OPTIONAL,
  -- per  vendor
  versionNumber  OCTET STRING(SIZE (1..256)) OPTIONAL,
  -- per productNumber
  ...
}

-- =============================================================================
-- Indication Message: New ATM virtual channel indication
-- =============================================================================
NewATMVCIndication ::= SEQUENCE {
  resourceID                   INTEGER(0..65535),
  bitRate                      INTEGER(1..65535), -- units 64 kbit/s
  bitRateLockedToPCRClock      BOOLEAN,
  bitRateLockedToNetworkClock  BOOLEAN,
  aal
    CHOICE {aal1
              SEQUENCE {clockRecovery
                          CHOICE {nullClockRecovery      NULL,
                                  srtsClockRecovery      NULL,
                                  adaptiveClockRecovery  NULL,
                                  ...},
                        errorCorrection
                          CHOICE {nullErrorCorrection  NULL,
                                  longInterleaver      NULL,
                                  shortInterleaver     NULL,
                                  errorCorrectionOnly  NULL,
                                  ...},
                        structuredDataTransfer  BOOLEAN,
                        partiallyFilledCells    BOOLEAN,
                        ...},
            aal5
              SEQUENCE {forwardMaximumSDUSize   INTEGER(0..65535), -- units octets--
                        backwardMaximumSDUSize  INTEGER(0..65535), -- units octets--
                        ...},
            ...},
  multiplex
    CHOICE {noMultiplex      NULL,
            transportStream  NULL,
            programStream    NULL,
            ...},
  ...,
  reverseParameters
    SEQUENCE {bitRate                      INTEGER(1..65535), -- units 64 kbit/s--
              bitRateLockedToPCRClock      BOOLEAN,
              bitRateLockedToNetworkClock  BOOLEAN,
              multiplex
                CHOICE {noMultiplex      NULL,
                        transportStream  NULL,
                        programStream    NULL,
                        ...},
              ...}
}

-- =============================================================================
-- Indication Message: User input
-- =============================================================================
IV8 ::= OCTET STRING(SIZE (8))

-- initial value for
-- 64-bit block ciphers
IV16 ::= OCTET STRING(SIZE (16))

-- initial value for
-- 128-bit block ciphers
Params ::= SEQUENCE {
  iv8   IV8 OPTIONAL, -- 8-octet initialization vector
  iv16  IV16 OPTIONAL, -- 16-octet initialization vector
  iv    OCTET STRING OPTIONAL, -- arbitrary length

  -- initialization vector
  ...
}

UserInputIndication ::= CHOICE {
  nonStandard                 NonStandardParameter,
  alphanumeric                GeneralString,
  ...,
  userInputSupportIndication
    CHOICE {nonStandard             NonStandardParameter,
            basicString             NULL, -- indicates unsecured basic string--
            iA5String               NULL, -- indicates unsecured IA5 string--
            generalString           NULL, -- indicates unsecured general string--
            ...,
            encryptedBasicString    NULL, -- indicates encrypted Basic string--
            encryptedIA5String      NULL, -- indicates encrypted IA5 string--
            encryptedGeneralString  NULL -- indicates encrypted general string
  },
  signal
    SEQUENCE {signalType
                IA5String(SIZE (1) ^ FROM ("0123456789#*ABCD!")),
              -- holds dummy "!" if encryptedSignalType
              -- is being used
              duration                 INTEGER(1..65535) OPTIONAL,
              -- milliseconds
              rtp
                SEQUENCE {timestamp             INTEGER(0..4294967295) OPTIONAL,
                          expirationTime        INTEGER(0..4294967295) OPTIONAL,
                          logicalChannelNumber  LogicalChannelNumber,
                          ...} OPTIONAL,
              ...,
              rtpPayloadIndication     NULL OPTIONAL,
              paramS                   Params OPTIONAL, -- any "runtime" parameters--
              encryptedSignalType      OCTET STRING(SIZE (1)) OPTIONAL,
              -- encrypted signalType
              algorithmOID             OBJECT IDENTIFIER OPTIONAL},
  signalUpdate
    SEQUENCE {duration  INTEGER(1..65535), -- milliseconds--
              rtp
                SEQUENCE {logicalChannelNumber  LogicalChannelNumber,
                          ...} OPTIONAL,
              ...},
  extendedAlphanumeric
    SEQUENCE {alphanumeric              GeneralString, -- holds empty string if--
              -- encryptedAlphanumeric is
              -- being used
              rtpPayloadIndication      NULL OPTIONAL,
              ...,
              encryptedAlphanumeric
                SEQUENCE {algorithmOID  OBJECT IDENTIFIER,
                          paramS        Params OPTIONAL, -- any "runtime" parameters--
                          encrypted     OCTET STRING, -- general string encrypted--
                          ...} OPTIONAL},
  encryptedAlphanumeric
    SEQUENCE {algorithmOID  OBJECT IDENTIFIER,
              paramS        Params OPTIONAL, -- any "runtime" parameters--
              encrypted     OCTET STRING, -- basic string encrypted--
              ...},
  genericInformation          SEQUENCE OF GenericInformation
}

-- generic information associated
-- with the message
-- =============================================================================
-- Indication Message: Flow Control
-- =============================================================================
FlowControlIndication ::= SEQUENCE {
  scope
    CHOICE {logicalChannelNumber  LogicalChannelNumber,
            resourceID            INTEGER(0..65535),
            wholeMultiplex        NULL},
  restriction
    CHOICE {maximumBitRate  INTEGER(0..16777215), -- units 100 bit/s--
            noRestriction   NULL},
  ...
}

-- =============================================================================
-- Indication Message: Mobile Multilink Reconfiguration indication
-- =============================================================================
MobileMultilinkReconfigurationIndication ::= SEQUENCE {
  sampleSize       INTEGER(1..255),
  samplesPerFrame  INTEGER(1..255),
  ...
}

END

-- Generated by Asnp, the ASN.1 pretty-printer of France Telecom R&D

