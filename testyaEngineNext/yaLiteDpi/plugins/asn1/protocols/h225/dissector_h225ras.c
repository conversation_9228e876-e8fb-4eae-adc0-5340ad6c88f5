#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdio.h>
#define PROTO_NAME "h225ras"
#define ASN_EMIT_DEBUG 1

#include "asn1_discovery.h"

static int dissect_h225_h225_RasMessage(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
      // Check minimum length for SNMP message
    if (nxt_mbuf_get_length(mbuf) < 10) {
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

      // Get raw data from mbuf
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);
    RasMessage_t *RsaInfo = NULL;
      printf("H225RAS: First 8 bytes: %02x %02x %02x %02x %02x %02x %02x %02x\n",
        data[0], data[1], data[2], data[3],
        data[4], data[5], data[6], data[7]);
        printf("len = %zu\n", data_len);
    // Try multiple decoding strategies for H.225 RAS
    asn_dec_rval_t decode_result;

    // Try uper_decode with 0 skip bits (standard alignment)
    decode_result = uper_decode(0, &asn_DEF_RasMessage, (void **)&RsaInfo, data, data_len, 0, 0);
    if (decode_result.code == RC_OK) {
        printf("H225RAS: UPER decode successful\n");
        printf("H225RAS: Message type present value: %d\n", RsaInfo->present);
    } else {
        printf("H225RAS: UPER decode failed (consumed: %zu bytes)\n", decode_result.consumed);
        if (RsaInfo) {
            ASN_STRUCT_FREE(asn_DEF_RasMessage, RsaInfo);
            RsaInfo = NULL;
        }
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // Extract and print important fields if decode was successful
    if (RsaInfo) {
        printf("=== H.225 RAS Message Details ===\n");
        printf("DEBUG: RasMessage present value: %d\n", RsaInfo->present);
        printf("DEBUG: Expected admissionRequest value: %d\n", RasMessage_PR_admissionRequest);
        printf("DEBUG: Expected bandwidthConfirm value: %d\n", RasMessage_PR_bandwidthConfirm);

        // Get message type
        const char *msg_type = "Unknown";

        switch (RsaInfo->present) {
            case RasMessage_PR_gatekeeperRequest:
                msg_type = "Gatekeeper Request";
                break;
            case RasMessage_PR_gatekeeperConfirm:
                msg_type = "Gatekeeper Confirm";
                break;
            case RasMessage_PR_gatekeeperReject:
                msg_type = "Gatekeeper Reject";
                break;
            case RasMessage_PR_registrationRequest:
                msg_type = "Registration Request";
                break;
            case RasMessage_PR_registrationConfirm:
                msg_type = "Registration Confirm";
                break;
            case RasMessage_PR_registrationReject:
                msg_type = "Registration Reject";
                break;
            case RasMessage_PR_admissionRequest:
                msg_type = "Admission Request";
                printf("Request Seq Num: %ld\n", RsaInfo->choice.admissionRequest.requestSeqNum);
                printf("Bandwidth: %ld\n", RsaInfo->choice.admissionRequest.bandWidth);
                printf("Call Reference Value: %ld\n", RsaInfo->choice.admissionRequest.callReferenceValue);

                // Debug destinationInfo parsing
                if (RsaInfo->choice.admissionRequest.destinationInfo) {
                    printf("DestinationInfo count: %d\n", (int)RsaInfo->choice.admissionRequest.destinationInfo->list.count);
                    for (int i = 0; i < RsaInfo->choice.admissionRequest.destinationInfo->list.count; i++) {
                        AliasAddress_t *alias = RsaInfo->choice.admissionRequest.destinationInfo->list.array[i];
                        if (alias) {
                            printf("DestinationInfo[%d] present: %d\n", i, alias->present);
                            switch (alias->present) {
                                case AliasAddress_PR_dialledDigits:
                                    printf("  dialledDigits: %.*s\n",
                                           (int)alias->choice.dialledDigits.size,
                                           alias->choice.dialledDigits.buf);
                                    break;
                                case AliasAddress_PR_h323_ID:
                                    printf("  h323-ID found\n");
                                    break;
                                case AliasAddress_PR_url_ID:
                                    printf("  url-ID found\n");
                                    break;
                                default:
                                    printf("  Other AliasAddress type: %d\n", alias->present);
                                    break;
                            }
                        }
                    }
                }
                break;
            case RasMessage_PR_admissionConfirm:
                msg_type = "Admission Confirm";
                printf("Request Seq Num: %ld\n", RsaInfo->choice.admissionConfirm.requestSeqNum);
                printf("Bandwidth: %ld\n", RsaInfo->choice.admissionConfirm.bandWidth);
                break;
            case RasMessage_PR_admissionReject:
                msg_type = "Admission Reject";
                printf("Request Seq Num: %ld\n", RsaInfo->choice.admissionReject.requestSeqNum);
                break;
            case RasMessage_PR_disengageRequest:
                msg_type = "Disengage Request";
                printf("Request Seq Num: %ld\n", RsaInfo->choice.disengageRequest.requestSeqNum);
                break;
            case RasMessage_PR_disengageConfirm:
                msg_type = "Disengage Confirm";
                printf("Request Seq Num: %ld\n", RsaInfo->choice.disengageConfirm.requestSeqNum);
                break;
            case RasMessage_PR_disengageReject:
                msg_type = "Disengage Reject";
                break;
            case RasMessage_PR_locationRequest:
                msg_type = "Location Request";
                break;
            case RasMessage_PR_locationConfirm:
                msg_type = "Location Confirm";
                break;
            case RasMessage_PR_locationReject:
                msg_type = "Location Reject";
                break;
            case RasMessage_PR_bandwidthRequest:
                msg_type = "Bandwidth Request";
                break;
            case RasMessage_PR_bandwidthConfirm:
                msg_type = "Bandwidth Confirm";
                break;
            case RasMessage_PR_bandwidthReject:
                msg_type = "Bandwidth Reject";
                break;
            default:
                msg_type = "Other RAS Message";
                break;
        }

        printf("RAS Message Type: %s\n", msg_type);
        printf("=================================\n");
    }

    precord_t *precord = nxt_session_create_record(engine, session);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    // Clean up the decoded structure
    if (RsaInfo) {
        ASN_STRUCT_FREE(asn_DEF_RasMessage, RsaInfo);
    }

    // nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    return decode_result.consumed > 0 ? decode_result.consumed : data_len;
}

static inline int h225ras_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
  return dissect_h225_h225_RasMessage(engine, session, mbuf);
}
// Schema registration function
static int h225ras_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
    /* 注册 schema */
    pschema_t *pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "H.225 Protocol");

    // Basic H.225 message fields
    pschema_register_field(pschema, "protocol", YA_FT_STRING, "Protocol");
    pschema_register_field(pschema, "version", YA_FT_STRING, "Version");
    pschema_register_field(pschema, "message_type", YA_FT_STRING, "Message Type");
    pschema_register_field(pschema, "protocol_type", YA_FT_STRING, "Protocol Type"); // RAS or CS
    pschema_register_field(pschema, "data_length", YA_FT_UINT32, "Data Length");
    pschema_register_field(pschema, "decode_status", YA_FT_STRING, "Decode Status");
    pschema_register_field(pschema, "decode_error", YA_FT_STRING, "Decode Error");

    // H.323 User Information fields
    pschema_register_field(pschema, "h323_uu_pdu_present", YA_FT_UINT32, "H323-UU-PDU Present");
    pschema_register_field(pschema, "user_data_present", YA_FT_UINT32, "User Data Present");

    // H.323 UU-PDU fields
    pschema_register_field(pschema, "h323_message_body_type", YA_FT_STRING, "H323 Message Body Type");
    pschema_register_field(pschema, "h245_tunnelling", YA_FT_UINT32, "H245 Tunnelling");
    pschema_register_field(pschema, "h245_control_present", YA_FT_UINT32, "H245 Control Present");
    pschema_register_field(pschema, "h245_control_count", YA_FT_UINT32, "H245 Control Count");
    pschema_register_field(pschema, "nonstandard_data_present", YA_FT_UINT32, "Non-standard Data Present");

    // Protocol identifier
    pschema_register_field(pschema, "protocol_identifier", YA_FT_STRING, "Protocol Identifier");

    // Call information
    pschema_register_field(pschema, "call_identifier", YA_FT_STRING, "Call Identifier");
    pschema_register_field(pschema, "conference_id", YA_FT_STRING, "Conference ID");

    // Endpoint information
    pschema_register_field(pschema, "source_info_present", YA_FT_UINT32, "Source Info Present");
    pschema_register_field(pschema, "dest_info_present", YA_FT_UINT32, "Destination Info Present");
    pschema_register_field(pschema, "dest_call_signalling_address", YA_FT_STRING, "Destination Call Signalling Address");

    // Setup specific fields
    pschema_register_field(pschema, "source_address", YA_FT_STRING, "Source Address");
    pschema_register_field(pschema, "dest_address", YA_FT_STRING, "Destination Address");
    pschema_register_field(pschema, "dest_extra_calling", YA_FT_STRING, "Destination Extra Calling");
    pschema_register_field(pschema, "dest_extra_call_info", YA_FT_STRING, "Destination Extra Call Info");

    // Facility specific fields
    pschema_register_field(pschema, "facility_reason", YA_FT_STRING, "Facility Reason");

    // Release complete specific fields
    pschema_register_field(pschema, "release_complete_reason", YA_FT_STRING, "Release Complete Reason");

    // H.245 tunnelling fields
    pschema_register_field(pschema, "h245_control_0_size", YA_FT_UINT32, "H245 Control 0 Size");
    pschema_register_field(pschema, "h245_control_0_sample", YA_FT_BYTES, "H245 Control 0 Sample");
    pschema_register_field(pschema, "h245_control_1_size", YA_FT_UINT32, "H245 Control 1 Size");
    pschema_register_field(pschema, "h245_control_1_sample", YA_FT_BYTES, "H245 Control 1 Sample");
    pschema_register_field(pschema, "h245_control_2_size", YA_FT_UINT32, "H245 Control 2 Size");
    pschema_register_field(pschema, "h245_control_2_sample", YA_FT_BYTES, "H245 Control 2 Sample");

    // Setup specific boolean fields
    pschema_register_field(pschema, "active_mc", YA_FT_UINT32, "Active MC");
    pschema_register_field(pschema, "media_wait_for_connect", YA_FT_UINT32, "Media Wait For Connect");
    pschema_register_field(pschema, "can_overlap_send", YA_FT_UINT32, "Can Overlap Send");

    // RAS message specific fields
    pschema_register_field(pschema, "request_seq_num", YA_FT_UINT32, "Request Sequence Number");
    pschema_register_field(pschema, "endpoint_identifier", YA_FT_STRING, "Endpoint Identifier");
    pschema_register_field(pschema, "gatekeeper_identifier", YA_FT_STRING, "Gatekeeper Identifier");
    pschema_register_field(pschema, "call_reference_value", YA_FT_UINT32, "Call Reference Value");
    pschema_register_field(pschema, "bandwidth", YA_FT_UINT32, "Bandwidth");
    pschema_register_field(pschema, "reject_reason", YA_FT_STRING, "Reject Reason");
    pschema_register_field(pschema, "disengage_reason", YA_FT_STRING, "Disengage Reason");

    // Error handling and validation fields
    pschema_register_field(pschema, "validation_error", YA_FT_STRING, "Validation Error");
    pschema_register_field(pschema, "asn1_decode_consumed", YA_FT_UINT32, "ASN.1 Decode Consumed Bytes");
    pschema_register_field(pschema, "user_user_ie_offset", YA_FT_UINT32, "User-User IE Offset");
    pschema_register_field(pschema, "user_user_ie_length", YA_FT_UINT32, "User-User IE Length");
    pschema_register_field(pschema, "parse_warnings", YA_FT_STRING, "Parse Warnings");

    return 0;
}


// H.225 dissector definition
static nxt_dissector_def_t gDissectorDef =
{
    .name         = PROTO_NAME,
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = h225ras_schema_reg,
    .dissectFun   = h225ras_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_PORT_PAYLOAD("udp", 1719, NULL),  // H.225 call signalling
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(h225)
{
    nxt_dissector_register(&gDissectorDef);
}


